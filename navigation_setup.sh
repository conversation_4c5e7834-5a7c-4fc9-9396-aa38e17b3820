#!/bin/bash

# Navigation System Setup Script for Steribot
# This script sets up the navigation system network for testing the Android app

echo "=== Steribot Navigation System Setup ==="
echo "This script will help you set up the navigation system for testing"
echo ""

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "Please run this script as root (use sudo)"
    exit 1
fi

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to get the current IP address
get_current_ip() {
    hostname -I | awk '{print $1}'
}

# Function to setup ROS environment
setup_ros_environment() {
    echo "Setting up ROS environment..."
    
    # Check if ROS is installed
    if ! command_exists roscore; then
        echo "ROS is not installed. Installing ROS Noetic..."
        
        # Add ROS repository
        sh -c 'echo "deb http://packages.ros.org/ros/ubuntu $(lsb_release -sc) main" > /etc/apt/sources.list.d/ros-latest.list'
        apt-key adv --keyserver 'hkp://keyserver.ubuntu.com:80' --recv-key C1CF6E31E6BADE8868B172B4F42ED6FBAB17C654
        
        # Update package list
        apt update
        
        # Install ROS Noetic
        apt install -y ros-noetic-desktop-full
        
        # Initialize rosdep
        rosdep init
        rosdep update
        
        # Setup environment
        echo "source /opt/ros/noetic/setup.bash" >> ~/.bashrc
        source ~/.bashrc
    else
        echo "ROS is already installed"
    fi
}

# Function to setup navigation packages
setup_navigation_packages() {
    echo "Installing navigation packages..."
    
    apt install -y \
        ros-noetic-navigation \
        ros-noetic-move-base \
        ros-noetic-amcl \
        ros-noetic-map-server \
        ros-noetic-gmapping \
        ros-noetic-robot-localization \
        ros-noetic-robot-state-publisher \
        ros-noetic-joint-state-publisher \
        ros-noetic-tf2-tools \
        ros-noetic-rosbridge-server \
        ros-noetic-web-video-server
}

# Function to setup network configuration
setup_network() {
    echo "Configuring network..."
    
    CURRENT_IP=$(get_current_ip)
    echo "Current IP address: $CURRENT_IP"
    
    # Configure static IP if needed
    read -p "Do you want to set a static IP address? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        read -p "Enter desired IP address (default: *************): " STATIC_IP
        STATIC_IP=${STATIC_IP:-*************}
        
        # Backup current network configuration
        cp /etc/netplan/*.yaml /etc/netplan/backup_$(date +%Y%m%d_%H%M%S).yaml
        
        # Create new netplan configuration
        cat > /etc/netplan/01-netcfg.yaml << EOF
network:
  version: 2
  renderer: networkd
  ethernets:
    eth0:
      dhcp4: no
      addresses:
        - $STATIC_IP/24
      gateway4: ***********
      nameservers:
        addresses: [*******, *******]
  wifis:
    wlan0:
      dhcp4: no
      addresses:
        - $STATIC_IP/24
      gateway4: ***********
      nameservers:
        addresses: [*******, *******]
      access-points:
        "YourWiFiSSID":
          password: "YourWiFiPassword"
EOF
        
        echo "Network configuration created. Apply with: sudo netplan apply"
    fi
}

# Function to create launch files
create_launch_files() {
    echo "Creating ROS launch files..."
    
    # Create workspace directory
    mkdir -p /opt/steribot_ws/src
    cd /opt/steribot_ws
    
    # Initialize workspace
    source /opt/ros/noetic/setup.bash
    catkin_make
    
    # Create navigation launch file
    mkdir -p src/steribot_navigation/launch
    cat > src/steribot_navigation/launch/navigation.launch << 'EOF'
<launch>
  <!-- Map server -->
  <node name="map_server" pkg="map_server" type="map_server" args="$(find steribot_navigation)/maps/map.yaml"/>
  
  <!-- AMCL -->
  <node pkg="amcl" type="amcl" name="amcl" output="screen">
    <param name="odom_frame_id" value="odom"/>
    <param name="odom_model_type" value="diff"/>
    <param name="base_frame_id" value="base_link"/>
    <param name="global_frame_id" value="map"/>
  </node>
  
  <!-- Move base -->
  <node pkg="move_base" type="move_base" respawn="false" name="move_base" output="screen">
    <rosparam file="$(find steribot_navigation)/config/costmap_common_params.yaml" command="load" ns="global_costmap" />
    <rosparam file="$(find steribot_navigation)/config/costmap_common_params.yaml" command="load" ns="local_costmap" />
    <rosparam file="$(find steribot_navigation)/config/local_costmap_params.yaml" command="load" />
    <rosparam file="$(find steribot_navigation)/config/global_costmap_params.yaml" command="load" />
    <rosparam file="$(find steribot_navigation)/config/base_local_planner_params.yaml" command="load" />
  </node>
  
  <!-- ROSBridge for web interface -->
  <include file="$(find rosbridge_server)/launch/rosbridge_websocket.launch">
    <arg name="port" value="9090"/>
  </include>
  
  <!-- Web video server -->
  <node name="web_video_server" pkg="web_video_server" type="web_video_server">
    <param name="port" value="8080"/>
  </node>
</launch>
EOF

    # Create web interface launch file
    cat > src/steribot_navigation/launch/web_interface.launch << 'EOF'
<launch>
  <!-- ROSBridge WebSocket -->
  <include file="$(find rosbridge_server)/launch/rosbridge_websocket.launch">
    <arg name="port" value="9090"/>
  </include>
  
  <!-- HTTP server for web interface -->
  <node name="http_server" pkg="steribot_navigation" type="http_server.py" output="screen">
    <param name="port" value="5000"/>
  </node>
</launch>
EOF

    # Create simple HTTP server for web interface
    mkdir -p src/steribot_navigation/scripts
    cat > src/steribot_navigation/scripts/http_server.py << 'EOF'
#!/usr/bin/env python3

import rospy
import http.server
import socketserver
import os
from threading import Thread

class WebHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/pad':
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            
            html_content = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>Steribot Navigation Interface</title>
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
                    .success { background-color: #d4edda; color: #155724; }
                    .info { background-color: #d1ecf1; color: #0c5460; }
                    button { padding: 10px 20px; margin: 5px; font-size: 16px; }
                </style>
            </head>
            <body>
                <h1>Steribot Navigation System</h1>
                <div class="status success">
                    <strong>Status:</strong> Navigation system is running
                </div>
                <div class="status info">
                    <strong>ROS Master:</strong> Connected<br>
                    <strong>Navigation:</strong> Ready<br>
                    <strong>Map:</strong> Loaded
                </div>
                
                <h2>Navigation Controls</h2>
                <button onclick="startMapping()">Start Mapping</button>
                <button onclick="startNavigation()">Start Navigation</button>
                <button onclick="stopAll()">Stop All</button>
                
                <h2>System Information</h2>
                <div id="system-info">
                    <p><strong>IP Address:</strong> """ + str(rospy.get_param('~ip_address', 'Unknown')) + """</p>
                    <p><strong>Port:</strong> 5000</p>
                    <p><strong>ROS Bridge:</strong> ws://localhost:9090</p>
                </div>
                
                <script>
                    function startMapping() {
                        alert('Mapping mode activated');
                        // Add ROS communication here
                    }
                    
                    function startNavigation() {
                        alert('Navigation mode activated');
                        // Add ROS communication here
                    }
                    
                    function stopAll() {
                        alert('All systems stopped');
                        // Add ROS communication here
                    }
                </script>
            </body>
            </html>
            """
            
            self.wfile.write(html_content.encode())
        else:
            super().do_GET()

def start_server():
    port = rospy.get_param('~port', 5000)
    
    with socketserver.TCPServer(("", port), WebHandler) as httpd:
        rospy.loginfo(f"HTTP server started on port {port}")
        httpd.serve_forever()

if __name__ == "__main__":
    rospy.init_node('http_server')
    
    # Start server in separate thread
    server_thread = Thread(target=start_server)
    server_thread.daemon = True
    server_thread.start()
    
    rospy.spin()
EOF

    chmod +x src/steribot_navigation/scripts/http_server.py
    
    # Build workspace
    catkin_make
}

# Function to create startup script
create_startup_script() {
    echo "Creating startup script..."
    
    cat > /usr/local/bin/start_steribot_navigation.sh << 'EOF'
#!/bin/bash

# Steribot Navigation System Startup Script

echo "Starting Steribot Navigation System..."

# Source ROS environment
source /opt/ros/noetic/setup.bash
source /opt/steribot_ws/devel/setup.bash

# Start roscore in background
roscore &
ROSCORE_PID=$!

# Wait for roscore to start
sleep 5

# Start navigation system
roslaunch steribot_navigation navigation.launch &
NAV_PID=$!

# Start web interface
roslaunch steribot_navigation web_interface.launch &
WEB_PID=$!

echo "Navigation system started!"
echo "Web interface available at: http://$(hostname -I | awk '{print $1}'):5000/pad"
echo ""
echo "To stop the system, run: sudo /usr/local/bin/stop_steribot_navigation.sh"

# Keep script running
wait
EOF

    cat > /usr/local/bin/stop_steribot_navigation.sh << 'EOF'
#!/bin/bash

echo "Stopping Steribot Navigation System..."

# Kill all ROS processes
pkill -f ros
pkill -f roscore
pkill -f roslaunch

echo "Navigation system stopped."
EOF

    chmod +x /usr/local/bin/start_steribot_navigation.sh
    chmod +x /usr/local/bin/stop_steribot_navigation.sh
}

# Main setup function
main() {
    echo "Starting Steribot Navigation System setup..."
    echo ""
    
    # Update system
    echo "Updating system packages..."
    apt update && apt upgrade -y
    
    # Install dependencies
    echo "Installing dependencies..."
    apt install -y python3-pip python3-rosdep python3-rosinstall python3-rosinstall-generator python3-wstool build-essential
    
    # Setup ROS
    setup_ros_environment
    
    # Setup navigation packages
    setup_navigation_packages
    
    # Setup network
    setup_network
    
    # Create launch files
    create_launch_files
    
    # Create startup scripts
    create_startup_script
    
    echo ""
    echo "=== Setup Complete ==="
    echo ""
    echo "To start the navigation system:"
    echo "  sudo /usr/local/bin/start_steribot_navigation.sh"
    echo ""
    echo "To stop the navigation system:"
    echo "  sudo /usr/local/bin/stop_steribot_navigation.sh"
    echo ""
    echo "Web interface will be available at:"
    echo "  http://$(get_current_ip):5000/pad"
    echo ""
    echo "Configure your Android app to connect to:"
    echo "  IP: $(get_current_ip)"
    echo "  Port: 5000"
    echo "  Endpoint: /pad"
    echo ""
}

# Run main function
main "$@"
