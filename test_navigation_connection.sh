#!/bin/bash

# Test script to verify navigation system connection
# Run this script to test if the navigation system is properly configured

echo "=== Steribot Navigation Connection Test ==="
echo ""

# Function to check if a port is open
check_port() {
    local host=$1
    local port=$2
    local timeout=5
    
    if timeout $timeout bash -c "echo >/dev/tcp/$host/$port" 2>/dev/null; then
        echo "✅ Port $port on $host is open"
        return 0
    else
        echo "❌ Port $port on $host is closed or unreachable"
        return 1
    fi
}

# Function to check HTTP endpoint
check_http() {
    local url=$1
    local expected_status=${2:-200}
    
    if command -v curl >/dev/null 2>&1; then
        local status=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout 5 "$url")
        if [ "$status" = "$expected_status" ]; then
            echo "✅ HTTP endpoint $url is accessible (status: $status)"
            return 0
        else
            echo "❌ HTTP endpoint $url returned status: $status (expected: $expected_status)"
            return 1
        fi
    else
        echo "⚠️  curl not available, skipping HTTP test for $url"
        return 1
    fi
}

# Function to check ROS
check_ros() {
    if command -v rostopic >/dev/null 2>&1; then
        if rostopic list >/dev/null 2>&1; then
            echo "✅ ROS is running and accessible"
            return 0
        else
            echo "❌ ROS is not running or not accessible"
            return 1
        fi
    else
        echo "⚠️  ROS not installed or not in PATH"
        return 1
    fi
}

# Get IP address to test
DEFAULT_IP="*************"
read -p "Enter navigation system IP address (default: $DEFAULT_IP): " TEST_IP
TEST_IP=${TEST_IP:-$DEFAULT_IP}

echo ""
echo "Testing connection to navigation system at: $TEST_IP"
echo ""

# Test network connectivity
echo "1. Testing basic network connectivity..."
if ping -c 1 -W 5 "$TEST_IP" >/dev/null 2>&1; then
    echo "✅ Network connectivity to $TEST_IP is working"
else
    echo "❌ Cannot reach $TEST_IP - check network connection"
    echo ""
    echo "Troubleshooting steps:"
    echo "- Ensure both devices are on the same network"
    echo "- Check if the IP address is correct"
    echo "- Verify firewall settings"
    exit 1
fi

echo ""

# Test required ports
echo "2. Testing required ports..."
check_port "$TEST_IP" 5000  # Web interface
check_port "$TEST_IP" 9090  # ROSBridge WebSocket
check_port "$TEST_IP" 8080  # Video server (optional)

echo ""

# Test HTTP endpoints
echo "3. Testing HTTP endpoints..."
check_http "http://$TEST_IP:5000/pad"
check_http "http://$TEST_IP:8080" 404  # Video server might return 404 for root

echo ""

# Test ROS (if running locally)
echo "4. Testing ROS system..."
if [ "$TEST_IP" = "localhost" ] || [ "$TEST_IP" = "127.0.0.1" ] || [ "$TEST_IP" = "$(hostname -I | awk '{print $1}')" ]; then
    check_ros
else
    echo "⚠️  ROS test skipped (testing remote system)"
fi

echo ""

# Test WebSocket connection (if wscat is available)
echo "5. Testing WebSocket connection..."
if command -v wscat >/dev/null 2>&1; then
    echo "Testing ROSBridge WebSocket..."
    timeout 5 wscat -c "ws://$TEST_IP:9090" -x '{"op":"call_service","service":"/rosapi/get_time","args":{}}' 2>/dev/null
    if [ $? -eq 0 ]; then
        echo "✅ WebSocket connection successful"
    else
        echo "❌ WebSocket connection failed"
    fi
else
    echo "⚠️  wscat not available, install with: npm install -g wscat"
fi

echo ""

# Summary and recommendations
echo "=== Test Summary ==="
echo ""

# Check if navigation system is likely working
if check_port "$TEST_IP" 5000 >/dev/null 2>&1 && check_http "http://$TEST_IP:5000/pad" >/dev/null 2>&1; then
    echo "🎉 Navigation system appears to be working correctly!"
    echo ""
    echo "Android app configuration:"
    echo "- Set navigation IP to: $TEST_IP"
    echo "- Port: 5000"
    echo "- Endpoint: /pad"
    echo ""
    echo "You can now test the Android app connection."
else
    echo "⚠️  Navigation system may not be fully configured."
    echo ""
    echo "Next steps:"
    echo "1. Run the navigation setup script: sudo ./navigation_setup.sh"
    echo "2. Start the navigation system: sudo /usr/local/bin/start_steribot_navigation.sh"
    echo "3. Check the logs for any errors"
    echo "4. Verify firewall settings"
fi

echo ""

# Additional diagnostic information
echo "=== Diagnostic Information ==="
echo "Test IP: $TEST_IP"
echo "Test time: $(date)"
echo "Local IP: $(hostname -I | awk '{print $1}')"
echo "Network interfaces:"
ip addr show | grep -E "inet.*scope global" | awk '{print "  " $2 " (" $NF ")"}'

echo ""
echo "To view this information again, run: ./test_navigation_connection.sh"
