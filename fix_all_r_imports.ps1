# PowerShell script to fix all R import issues in the project

Write-Host "🔧 Fixing all R import issues in the project..." -ForegroundColor Green

# List of files that need R imports (from our previous search)
$filesToFix = @(
    "app/src/main/java/com/reeman/robot/disinfection/contract/LoginContract.java",
    "app/src/main/java/com/reeman/robot/disinfection/contract/MainContract.java",
    "app/src/main/java/com/reeman/robot/disinfection/contract/MapBuildingContract.java",
    "app/src/main/java/com/reeman/robot/disinfection/contract/ScheduledTaskListContract.java",
    "app/src/main/java/com/reeman/robot/disinfection/contract/SettingContract.java",
    "app/src/main/java/com/reeman/robot/disinfection/contract/TaskCreateContract.java",
    "app/src/main/java/com/reeman/robot/disinfection/contract/TaskExecutingContract.java",
    "app/src/main/java/com/reeman/robot/disinfection/contract/WiFiConnectContract.java",
    "app/src/main/java/com/reeman/robot/disinfection/plugin/Log.java",
    "app/src/main/java/com/reeman/robot/disinfection/presenter/impl/ScheduledTaskListPresenter.java",
    "app/src/main/java/com/reeman/robot/disinfection/proxy/MainPresenterHandler.java",
    "app/src/main/java/com/reeman/robot/disinfection/receiver/RobotReceiver.java",
    "app/src/main/java/com/reeman/robot/disinfection/repository/DbRepository.java",
    "app/src/main/java/com/reeman/robot/disinfection/repository/db/AppDataBase.java",
    "app/src/main/java/com/reeman/robot/disinfection/request/RetrofitClient.java",
    "app/src/main/java/com/reeman/robot/disinfection/service/BackendService.java",
    "app/src/main/java/com/reeman/robot/disinfection/utils/PackageUtils.java",
    "app/src/main/java/com/reeman/robot/disinfection/utils/QRCodeUtils.java",
    "app/src/main/java/com/reeman/robot/disinfection/utils/SizeUtils.java",
    "app/src/main/java/com/reeman/robot/disinfection/utils/SoftKeyboardStateWatcher.java",
    "app/src/main/java/com/reeman/robot/disinfection/utils/SpManager.java",
    "app/src/main/java/com/reeman/robot/disinfection/utils/VoiceHelper.java",
    "app/src/main/java/com/reeman/robot/disinfection/utils/WIFIUtils.java",
    "app/src/main/java/com/reeman/robot/disinfection/widgets/DrawView.java",
    "app/src/main/java/com/reeman/robot/disinfection/widgets/FourCircleRotate.java",
    "app/src/main/java/com/reeman/robot/disinfection/widgets/GridRadioGroup.java",
    "app/src/main/java/com/reeman/robot/disinfection/widgets/GuideView.java",
    "app/src/main/java/com/reeman/robot/disinfection/widgets/ListViewForScrollView.java",
    "app/src/main/java/com/reeman/robot/disinfection/widgets/MapWebViewChromeClient.java",
    "app/src/main/java/com/reeman/robot/disinfection/widgets/MapWebViewClient.java",
    "app/src/main/java/com/reeman/robot/disinfection/widgets/UserHintView.java",
    "app/src/main/java/com/reeman/robot/disinfection/widgets/ViewBuilder.java",
    "app/src/main/java/com/reeman/robot/disinfection/widgets/WebViewHolder.java"
)

$fixedCount = 0
$errorCount = 0

foreach ($file in $filesToFix) {
    if (Test-Path $file) {
        try {
            $content = Get-Content $file -Raw
            
            # Check if file uses R. but doesn't have R import
            if ($content -match "R\." -and $content -notmatch "import.*\.R;") {
                Write-Host "Fixing: $file" -ForegroundColor Yellow
                
                # Find the package declaration line
                $lines = Get-Content $file
                $packageLineIndex = -1
                $lastImportIndex = -1
                
                for ($i = 0; $i -lt $lines.Length; $i++) {
                    if ($lines[$i] -match "^package ") {
                        $packageLineIndex = $i
                    }
                    if ($lines[$i] -match "^import ") {
                        $lastImportIndex = $i
                    }
                }
                
                # Insert R import after the last import or after package if no imports
                $insertIndex = if ($lastImportIndex -ge 0) { $lastImportIndex + 1 } else { $packageLineIndex + 2 }
                
                # Create new content with R import
                $newLines = @()
                for ($i = 0; $i -lt $lines.Length; $i++) {
                    $newLines += $lines[$i]
                    if ($i -eq $insertIndex - 1) {
                        if ($lastImportIndex -ge 0) {
                            $newLines += "import com.reeman.robot.disinfection.R;"
                        } else {
                            $newLines += ""
                            $newLines += "import com.reeman.robot.disinfection.R;"
                        }
                    }
                }
                
                # Write back to file
                $newLines | Set-Content $file -Encoding UTF8
                $fixedCount++
                Write-Host "✅ Fixed: $file" -ForegroundColor Green
            } else {
                Write-Host "⏭️  Skipped: $file (no R usage or already has import)" -ForegroundColor Gray
            }
        } catch {
            Write-Host "❌ Error fixing: $file - $($_.Exception.Message)" -ForegroundColor Red
            $errorCount++
        }
    } else {
        Write-Host "⚠️  File not found: $file" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "📊 Summary:" -ForegroundColor Cyan
Write-Host "✅ Files fixed: $fixedCount" -ForegroundColor Green
Write-Host "❌ Errors: $errorCount" -ForegroundColor Red
Write-Host ""

if ($fixedCount -gt 0) {
    Write-Host "🔨 Building project to verify fixes..." -ForegroundColor Yellow
    & ./gradlew assembleDebug
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "🎉 Build successful! All R import issues fixed!" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Build failed. Some issues may remain." -ForegroundColor Yellow
    }
} else {
    Write-Host "ℹ️  No files needed fixing." -ForegroundColor Blue
}
