# Steribot Android Application - Backend Architecture Documentation

## 📋 Executive Summary

The Steribot Android application is a comprehensive robotic disinfection system that integrates multiple backend components including local database management, cloud synchronization, ROS (Robot Operating System) communication, and navigation system integration. This document provides a complete analysis of the original backend architecture before any modifications.

## 🏗️ System Architecture Overview

### **Core Components**
1. **Local Database Layer** - SQLite with Room ORM
2. **Cloud API Layer** - RESTful API communication
3. **ROS Integration Layer** - Robot Operating System communication
4. **Navigation System** - Real-time robot navigation and mapping
5. **Background Services** - Data synchronization and monitoring
6. **Authentication System** - Token-based authentication

### **Architecture Pattern**
- **MVP (Model-View-Presenter)** pattern for UI components
- **Repository Pattern** for data access abstraction
- **Singleton Pattern** for controllers and services
- **Observer Pattern** for event handling

## 🗄️ Database Architecture

### **Primary Database: SQLite with Room ORM**

#### **Database Configuration**
```java
@Database(entities = {Task.class}, exportSchema = false, version = 1)
@TypeConverters(DateConverter.class)
public abstract class AppDataBase extends RoomDatabase {
    private static final String DB_NAME = "db_task";
    public abstract TaskDao taskDao();
}
```

#### **Core Entity: Task**
**Table Name:** `t_task`

**Primary Fields:**
- `tid` (BIGINT, PRIMARY KEY, AUTO_INCREMENT) - Local task ID
- `t_cloud_id` (BIGINT) - Cloud synchronization ID
- `t_task_type` (INTEGER) - Task type (0: Manual, 1: Scheduled)
- `t_task_name` (TEXT) - Task name
- `t_task_mode` (INTEGER) - Execution mode (0: Single, 1: Duration loop)
- `t_switch_mode` (INTEGER) - Disinfection switch mode (0: Always on, 1: Target points only, 2: Always off)
- `t_finish_action` (INTEGER) - Post-completion action (0: Charge, 1: Return to start)
- `t_stay_time` (BIGINT) - Stay duration at target points (seconds)
- `t_duration_time` (BIGINT) - Loop duration (seconds)
- `t_start_time` (TEXT) - Scheduled start time
- `t_repeat_time` (INTEGER) - Weekly repeat pattern (bitmask)
- `t_enabled` (BOOLEAN) - Task enabled status
- `t_create_time` (DATETIME) - Creation timestamp
- `t_has_sync` (BOOLEAN) - Cloud synchronization status
- `t_has_delete` (BOOLEAN) - Soft delete flag

#### **Data Access Layer (DAO)**
```java
@Dao
public interface TaskDao {
    // CRUD Operations
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    Single<Long> newTask(Task task);
    
    @Update(onConflict = OnConflictStrategy.REPLACE)
    Single<Integer> updateTask(Task task);
    
    @Query("UPDATE T_TASK SET t_has_delete = 1, t_has_sync = 0 WHERE tid = :tid")
    Single<Integer> deleteTask(long tid);
    
    // Query Operations
    @Query("SELECT * FROM T_TASK WHERE t_task_type = 0")
    Maybe<Task> getManualTask();
    
    @Query("SELECT * FROM T_TASK WHERE t_task_type = 1 AND t_has_delete = 0 ORDER BY t_start_time DESC")
    Single<List<Task>> getAllScheduledTask();
    
    // Synchronization Queries
    @Query("SELECT * FROM T_TASK WHERE t_task_type = 1 AND t_has_sync = 0")
    List<Task> getAllUnSyncTask();
    
    @Query("SELECT * FROM T_TASK WHERE t_task_type = 1 AND t_cloud_id = -1 AND t_has_sync = 0 AND t_has_delete = 0")
    List<Task> getAllUnCreatedTasks();
    
    @Query("SELECT * FROM T_TASK WHERE t_task_type = 1 AND t_cloud_id != -1 AND t_has_sync = 0 AND t_has_delete = 0")
    List<Task> getAllModifiedTask();
}
```

#### **Repository Pattern Implementation**
```java
public class DbRepository {
    private static DbRepository sInstance;
    private final AppDataBase database;
    
    public static DbRepository getInstance(final AppDataBase database) {
        if (sInstance == null) {
            synchronized (DbRepository.class) {
                if (sInstance == null) {
                    sInstance = new DbRepository(database);
                }
            }
        }
        return sInstance;
    }
}
```

## 🌐 Cloud API Architecture

### **Base API Configuration**
- **Base URL:** Configurable via RetrofitClient
- **Authentication:** Bearer token-based
- **Protocol:** HTTPS RESTful API
- **Data Format:** JSON

### **API Endpoints**

#### **Authentication Endpoints**
```java
@POST("/openapispring/tokens")
Observable<LoginResponse> login(@Body Map<String, String> loginModel);
```

#### **Task Management Endpoints**
```java
// Get all online tasks
@GET("/openapispring/disinfectionrobots/{hostname}/tasks?page=1&size=100")
Call<TaskListResponse> getAllOnlineTask(@Path("hostname") String hostname);

// Create new task
@POST("/openapispring/disinfectionrobots/{hostname}/tasks")
Observable<TaskUpdateResponse> newOnlineTask(@Path("hostname") String hostname, @Body OnlineTask onlineTask);

// Update existing task
@PUT("/openapispring/disinfectionrobots/{hostname}/tasks/{taskId}")
Observable<TaskUpdateResponse> updateOnlineTask(@Path("hostname") String hostname, @Path("taskId") long taskId, @Body OnlineTask onlineTask);

// Delete task
@DELETE("/openapispring/disinfectionrobots/{hostname}/tasks/{taskId}")
Call<BaseResponse> deleteOnlineTask(@Path("hostname") String hostname, @Path("taskId") long taskId);

// Update task enable state
@PATCH("/openapispring/disinfectionrobots/{hostname}/tasks/{taskId}")
Call<TaskUpdateResponse> updateOnlineTaskEnableState(@Path("hostname") String hostname, @Path("taskId") long taskId, @Body Map<String, Integer> map);
```

#### **Robot State Synchronization**
```java
@PUT("/openapispring/disinfectionrobots/{hostname}")
Call<Map<String, Object>> syncState(@Body State state, @Path("hostname") String hostname);
```

#### **Map and Navigation Endpoints**
```java
@GET
Observable<List<MapListResp>> getMapList(@Url String url);

@GET
Call<Map<String, List<Point>>> fetchPoints(@Url String url);
```

#### **Task Logging**
```java
@POST("/openapispring/disinfectionrobots/{hostname}/tasks/logs")
Observable<Map<String, String>> syncTaskRecord(@Path("hostname") String hostname, @Body TaskRecord record);
```

### **Authentication Flow**
1. **Initial Login:** Username/password → Access token + Refresh token
2. **Token Storage:** Secure storage in SharedPreferences
3. **Auto-refresh:** Automatic token renewal on 401 responses
4. **Request Interceptor:** Automatic token attachment to requests

```java
// Token refresh logic in RetrofitClient
if (response.code() == 401) {
    // Auto-login with stored credentials
    LoginResponse loginResponse = ServiceFactory.getRobotService().loginSync(map).execute().body();
    String newAccessToken = loginResponse.data.result.accessToken;
    // Update stored tokens and retry request
}
```

## 🤖 ROS Integration Architecture

### **ROS Controller System**
The application integrates with ROS (Robot Operating System) for robot control and navigation.

#### **Core Controllers**
1. **RobotActionController** - Main robot control interface
2. **NavigationController** - Navigation state management

#### **RobotActionController Integration**
```java
public class RobotActionController {
    // Initialization
    controller.init(context, logLevel, logDirectory);
    
    // Navigation commands
    controller.navigationByPoint(pointName);
    controller.positionAutoUploadControl(enabled);
    
    // Hardware control
    controller.generalPowerControl(enabled); // UV control
    controller.humanDetectionControl(enabled); // Human detection
}
```

#### **NavigationController State Management**
```java
public class NavigationController {
    private boolean isNavigating = false;
    private boolean isChargingDocking = false;
    private int level = 0; // Battery level
    private int plug = 0; // Charging status
    private int emergencyStop = -1; // Emergency stop state
    private boolean isFall = false; // Fall detection
    private String lastSensorState = "";
    
    // Navigation control
    public void navigationByPoint(String point) {
        isNavigating = true;
        controller.navigationByPoint(point);
    }
    
    // Hardware control
    public void UVControl(boolean open) {
        controller.generalPowerControl(open);
    }
    
    public void humanDetectionControl(boolean open) {
        if (openDetection) {
            controller.humanDetectionControl(open);
        }
    }
}
```

### **ROS Communication Protocol**
- **Transport:** ROS topics and services
- **Message Format:** ROS standard messages
- **Network:** TCP/IP over WiFi
- **Discovery:** ROS Master node discovery

## 🗺️ Navigation System Architecture

### **Navigation Components**
1. **Map Server** - Provides map data for navigation
2. **AMCL (Adaptive Monte Carlo Localization)** - Robot localization
3. **Move Base** - Path planning and execution
4. **Costmap** - Obstacle avoidance mapping

### **Navigation System Integration**
- **Default IP:** *************
- **Default Port:** 5000
- **Web Interface Endpoint:** `/pad`
- **ROS Bridge Port:** 9090
- **Video Stream Port:** 8080

### **Navigation Communication Flow**
1. **WebView Integration** - Loads navigation interface at `http://IP:5000/pad`
2. **ROS Bridge** - WebSocket communication for real-time data
3. **Map Building** - Interactive map creation and editing
4. **Point Navigation** - Target point selection and navigation

## 🔄 Background Services Architecture

### **BackendService - Data Synchronization**
Continuous background service for cloud synchronization:

```java
public class BackendService extends Service {
    // Synchronization operations every 30 seconds
    private void performSyncOperations() {
        // 1. Delete cloud tasks marked for deletion locally
        List<Task> deletedTasks = dbRepository.getAllDeleteAndUnSyncTasks();
        
        // 2. Upload new local tasks to cloud
        List<Task> unCreatedTasks = dbRepository.getAllUnCreatedTasks();
        
        // 3. Update modified tasks in cloud
        List<Task> modifiedTasks = dbRepository.getAllModifiedTask();
        
        // 4. Download and merge cloud tasks
        TaskListResponse response = robotService.getAllOnlineTask(hostname).execute();
    }
}
```

### **Event System**
- **EventBus** - Decoupled component communication
- **RobotReceiver** - System broadcast receiver
- **Custom Events** - Application-specific event handling

## 🔐 Security Architecture

### **Authentication Security**
- **Token-based Authentication** - JWT tokens for API access
- **Automatic Token Refresh** - Seamless token renewal
- **Secure Storage** - Encrypted SharedPreferences for credentials

### **Network Security**
- **HTTPS Communication** - Encrypted API communication
- **Request Logging** - Comprehensive request/response logging
- **Error Handling** - Graceful error handling and recovery

### **Data Security**
- **Local Encryption** - Sensitive data encryption
- **Soft Delete** - Data recovery capability
- **Audit Trail** - Complete operation logging

## 📊 Performance Architecture

### **Database Performance**
- **Reactive Programming** - RxJava for asynchronous operations
- **Connection Pooling** - Efficient database connections
- **Query Optimization** - Indexed queries for performance

### **Network Performance**
- **Connection Pooling** - HTTP connection reuse
- **Request Caching** - Intelligent response caching
- **Retry Logic** - Automatic retry with exponential backoff

### **Memory Management**
- **Singleton Pattern** - Controlled object instantiation
- **Lifecycle Management** - Proper resource cleanup
- **Background Processing** - Off-main-thread operations

## 🔧 Configuration Management

### **Application Configuration**
```java
public class Constant {
    // Database configuration
    public static final String SP_NAME = "disinfection_robot";
    
    // Navigation configuration
    public static final String NAV_SPEED = "nav_speed";
    public static final String DEFAULT_NAV_SPEED = "0.4";
    
    // System configuration
    public static final String LOW_POWER = "low_power";
    public static final int DEFAULT_LOW_POWER = 20;
    
    // Authentication configuration
    public static final String USERNAME = "username";
    public static final String PASSWORD = "password";
    public static final String ACCESS_TOKEN = "accessToken";
    public static final String REFRESH_TOKEN = "refreshToken";
}
```

### **Network Configuration**
- **Dynamic IP Detection** - Automatic navigation system discovery
- **Configurable Endpoints** - Flexible API endpoint configuration
- **Fallback Mechanisms** - Graceful degradation on network issues

This architecture provides a robust, scalable, and maintainable foundation for the Steribot robotic disinfection system, integrating local data management, cloud synchronization, real-time robot control, and navigation capabilities.
