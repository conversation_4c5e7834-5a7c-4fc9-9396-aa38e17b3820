#!/usr/bin/env python3
"""
Mock Steribot Navigation System
Simulates the navigation system for testing the Android app
"""

import http.server
import socketserver
import socket
import json
import time
from urllib.parse import urlparse, parse_qs

class MockNavigationHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/pad':
            self.serve_navigation_interface()
        elif self.path == '/status':
            self.serve_status_json()
        elif self.path.startswith('/api/'):
            self.serve_api_response()
        else:
            self.send_response(404)
            self.send_header('Content-type', 'text/plain')
            self.end_headers()
            self.wfile.write(b'404 - Not Found')
    
    def do_POST(self):
        if self.path.startswith('/api/'):
            self.serve_api_response()
        else:
            self.send_response(404)
            self.end_headers()
    
    def serve_navigation_interface(self):
        self.send_response(200)
        self.send_header('Content-type', 'text/html')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>Mock Steribot Navigation System</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {{ 
            font-family: Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }}
        .status {{
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            background: rgba(40, 167, 69, 0.2);
            border: 2px solid #28a745;
            text-align: center;
        }}
        .controls {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 30px 0;
        }}
        button {{
            padding: 15px 25px;
            font-size: 16px;
            font-weight: bold;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
        }}
        .btn-primary {{
            background: #007bff;
            color: white;
        }}
        .btn-primary:hover {{
            background: #0056b3;
            transform: translateY(-2px);
        }}
        .btn-success {{
            background: #28a745;
            color: white;
        }}
        .btn-success:hover {{
            background: #1e7e34;
            transform: translateY(-2px);
        }}
        .btn-danger {{
            background: #dc3545;
            color: white;
        }}
        .btn-danger:hover {{
            background: #c82333;
            transform: translateY(-2px);
        }}
        .info-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }}
        .info-card {{
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }}
        .map-area {{
            background: rgba(0, 0, 0, 0.3);
            height: 300px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px 0;
            border: 2px dashed rgba(255, 255, 255, 0.3);
        }}
        .robot-status {{
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
        }}
        .status-item {{
            text-align: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            min-width: 120px;
        }}
        .status-value {{
            font-size: 24px;
            font-weight: bold;
            color: #28a745;
        }}
        @keyframes pulse {{
            0% {{ opacity: 1; }}
            50% {{ opacity: 0.5; }}
            100% {{ opacity: 1; }}
        }}
        .online {{
            animation: pulse 2s infinite;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 Mock Steribot Navigation System</h1>
        
        <div class="status online">
            <h2>✅ System Status: ONLINE</h2>
            <p>Mock navigation system is running and ready for Android app connection</p>
            <p><strong>Server IP:</strong> {self.get_local_ip()} | <strong>Port:</strong> 5000</p>
        </div>
        
        <div class="robot-status">
            <div class="status-item">
                <div class="status-value">85%</div>
                <div>Battery</div>
            </div>
            <div class="status-item">
                <div class="status-value">IDLE</div>
                <div>Status</div>
            </div>
            <div class="status-item">
                <div class="status-value">READY</div>
                <div>Navigation</div>
            </div>
            <div class="status-item">
                <div class="status-value">OFF</div>
                <div>UV System</div>
            </div>
        </div>
        
        <h2>🗺️ Navigation Map</h2>
        <div class="map-area">
            <div style="text-align: center;">
                <h3>📍 Interactive Map Area</h3>
                <p>Click to set waypoints • Drag to navigate • Right-click for options</p>
                <p style="opacity: 0.7;">This is a mock interface for testing purposes</p>
            </div>
        </div>
        
        <h2>🎮 Robot Controls</h2>
        <div class="controls">
            <button class="btn-primary" onclick="startMapping()">🗺️ Start Mapping</button>
            <button class="btn-success" onclick="startNavigation()">🧭 Start Navigation</button>
            <button class="btn-primary" onclick="setWaypoint()">📍 Set Waypoint</button>
            <button class="btn-success" onclick="startDisinfection()">🦠 Start Disinfection</button>
            <button class="btn-primary" onclick="returnToBase()">🏠 Return to Base</button>
            <button class="btn-danger" onclick="emergencyStop()">🛑 Emergency Stop</button>
        </div>
        
        <div class="info-grid">
            <div class="info-card">
                <h3>📡 Connection Info</h3>
                <p><strong>Protocol:</strong> HTTP/WebSocket</p>
                <p><strong>API Endpoint:</strong> /api/v1/</p>
                <p><strong>Status:</strong> Connected</p>
                <p><strong>Latency:</strong> <span id="latency">12ms</span></p>
            </div>
            
            <div class="info-card">
                <h3>🤖 Robot Information</h3>
                <p><strong>Model:</strong> Steribot Pro</p>
                <p><strong>Serial:</strong> SB-2024-001</p>
                <p><strong>Firmware:</strong> v4.0.3</p>
                <p><strong>Last Update:</strong> <span id="timestamp">{time.strftime('%Y-%m-%d %H:%M:%S')}</span></p>
            </div>
            
            <div class="info-card">
                <h3>📊 Performance</h3>
                <p><strong>Uptime:</strong> <span id="uptime">2h 34m</span></p>
                <p><strong>Tasks Completed:</strong> 15</p>
                <p><strong>Distance Traveled:</strong> 2.3 km</p>
                <p><strong>Area Disinfected:</strong> 450 m²</p>
            </div>
            
            <div class="info-card">
                <h3>⚙️ System Health</h3>
                <p><strong>CPU Usage:</strong> 23%</p>
                <p><strong>Memory:</strong> 1.2GB / 4GB</p>
                <p><strong>Storage:</strong> 45GB / 128GB</p>
                <p><strong>Temperature:</strong> 42°C</p>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 30px; opacity: 0.8;">
            <p>🔧 This is a mock navigation system for testing the Steribot Android application</p>
            <p>📱 Configure your Android app to connect to: <strong>{self.get_local_ip()}:5000</strong></p>
        </div>
    </div>
    
    <script>
        // Mock JavaScript functions
        function startMapping() {{
            updateStatus('🗺️ Mapping mode activated - Building environment map...');
        }}
        
        function startNavigation() {{
            updateStatus('🧭 Navigation mode activated - Ready to navigate to waypoints');
        }}
        
        function setWaypoint() {{
            updateStatus('📍 Waypoint mode activated - Click on map to set destination');
        }}
        
        function startDisinfection() {{
            updateStatus('🦠 Disinfection started - UV system activated');
        }}
        
        function returnToBase() {{
            updateStatus('🏠 Returning to charging station...');
        }}
        
        function emergencyStop() {{
            updateStatus('🛑 EMERGENCY STOP - All systems halted');
        }}
        
        function updateStatus(message) {{
            alert(message);
            console.log('Navigation Command:', message);
        }}
        
        // Update timestamp every second
        setInterval(() => {{
            document.getElementById('timestamp').textContent = new Date().toLocaleString();
        }}, 1000);
        
        // Simulate latency updates
        setInterval(() => {{
            const latency = Math.floor(Math.random() * 20) + 5;
            document.getElementById('latency').textContent = latency + 'ms';
        }}, 3000);
        
        // Update uptime
        let startTime = Date.now();
        setInterval(() => {{
            const elapsed = Date.now() - startTime;
            const hours = Math.floor(elapsed / 3600000);
            const minutes = Math.floor((elapsed % 3600000) / 60000);
            document.getElementById('uptime').textContent = hours + 'h ' + minutes + 'm';
        }}, 60000);
        
        console.log('🤖 Mock Steribot Navigation System loaded successfully');
        console.log('📱 Android app can now connect to this interface');
    </script>
</body>
</html>
        """
        
        self.wfile.write(html_content.encode())
    
    def serve_status_json(self):
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        status = {
            'status': 'online',
            'timestamp': time.time(),
            'robot': {
                'battery': 85,
                'status': 'idle',
                'navigation': 'ready',
                'uv_system': 'off',
                'position': {'x': 0, 'y': 0, 'theta': 0}
            },
            'system': {
                'uptime': 9240,  # seconds
                'cpu_usage': 23,
                'memory_usage': 30,
                'temperature': 42
            }
        }
        
        self.wfile.write(json.dumps(status, indent=2).encode())
    
    def serve_api_response(self):
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        response = {
            'success': True,
            'message': 'Mock API response',
            'timestamp': time.time(),
            'data': {}
        }
        
        self.wfile.write(json.dumps(response).encode())
    
    def get_local_ip(self):
        try:
            # Get local IP address
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip = s.getsockname()[0]
            s.close()
            return ip
        except:
            return "localhost"
    
    def log_message(self, format, *args):
        # Custom log format
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] {format % args}")

if __name__ == "__main__":
    PORT = 5000
    
    print("🤖 Starting Mock Steribot Navigation System...")
    print("=" * 50)
    
    try:
        with socketserver.TCPServer(("", PORT), MockNavigationHandler) as httpd:
            local_ip = MockNavigationHandler(None, None, None).get_local_ip()
            
            print(f"✅ Mock navigation system started successfully!")
            print(f"📡 Server running at: http://{local_ip}:{PORT}")
            print(f"🌐 Navigation interface: http://{local_ip}:{PORT}/pad")
            print(f"📊 Status API: http://{local_ip}:{PORT}/status")
            print()
            print("📱 Android App Configuration:")
            print(f"   • Set navigation IP to: {local_ip}")
            print(f"   • Port: {PORT}")
            print(f"   • Endpoint: /pad")
            print()
            print("🔧 Testing URLs:")
            print(f"   • Web browser: http://{local_ip}:{PORT}/pad")
            print(f"   • Status check: http://{local_ip}:{PORT}/status")
            print()
            print("🛑 Press Ctrl+C to stop the server")
            print("=" * 50)
            
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except OSError as e:
        if e.errno == 10048:  # Port already in use
            print(f"❌ Error: Port {PORT} is already in use")
            print("   • Stop any other services using port 5000")
            print("   • Or change the PORT variable in this script")
        else:
            print(f"❌ Error starting server: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
