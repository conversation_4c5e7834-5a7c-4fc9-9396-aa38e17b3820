package com.example.workomar.verification;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.example.workomar.config.NetworkConfig;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.URL;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Verifies navigation system connectivity and prevents bypassing interfaces
 */
public class NavigationSystemVerifier {
    
    private static final String TAG = "NavSystemVerifier";
    private static NavigationSystemVerifier instance;
    
    private final Context context;
    private final NetworkConfig networkConfig;
    private final ExecutorService executor;
    private final Handler mainHandler;
    
    private boolean isNavigationSystemConnected = false;
    private boolean isVerificationInProgress = false;
    private long lastVerificationTime = 0;
    private static final long VERIFICATION_CACHE_TIME = 30000; // 30 seconds
    
    public interface VerificationCallback {
        void onVerificationComplete(boolean isConnected, String message);
        void onVerificationFailed(String error);
    }
    
    private NavigationSystemVerifier(Context context) {
        this.context = context.getApplicationContext();
        this.networkConfig = NetworkConfig.getInstance(context);
        this.executor = Executors.newSingleThreadExecutor();
        this.mainHandler = new Handler(Looper.getMainLooper());
    }
    
    public static synchronized NavigationSystemVerifier getInstance(Context context) {
        if (instance == null) {
            instance = new NavigationSystemVerifier(context);
        }
        return instance;
    }
    
    /**
     * Verify navigation system connectivity with comprehensive checks
     */
    public void verifyNavigationSystem(VerificationCallback callback) {
        // Check if verification is already in progress
        if (isVerificationInProgress) {
            callback.onVerificationFailed("Verification already in progress");
            return;
        }
        
        // Use cached result if recent
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastVerificationTime < VERIFICATION_CACHE_TIME) {
            callback.onVerificationComplete(isNavigationSystemConnected, 
                isNavigationSystemConnected ? "Navigation system connected (cached)" : "Navigation system not connected (cached)");
            return;
        }
        
        isVerificationInProgress = true;
        
        executor.execute(() -> {
            try {
                VerificationResult result = performComprehensiveVerification();
                
                mainHandler.post(() -> {
                    isNavigationSystemConnected = result.isConnected;
                    lastVerificationTime = currentTime;
                    isVerificationInProgress = false;
                    
                    if (result.isConnected) {
                        callback.onVerificationComplete(true, result.message);
                    } else {
                        callback.onVerificationFailed(result.message);
                    }
                });
                
            } catch (Exception e) {
                Log.e(TAG, "Verification failed with exception", e);
                mainHandler.post(() -> {
                    isVerificationInProgress = false;
                    callback.onVerificationFailed("Verification failed: " + e.getMessage());
                });
            }
        });
    }
    
    /**
     * Perform comprehensive verification of navigation system
     */
    private VerificationResult performComprehensiveVerification() {
        String navIP = networkConfig.getNavigationIP();
        int navPort = networkConfig.getNavigationPort();
        
        Log.i(TAG, "Starting verification for " + navIP + ":" + navPort);
        
        // Step 1: Check network connectivity
        if (!networkConfig.isNetworkConnected()) {
            return new VerificationResult(false, "No network connection available");
        }
        
        // Step 2: Check if navigation system port is reachable
        if (!isPortReachable(navIP, navPort, 5000)) {
            return new VerificationResult(false, 
                "Navigation system port " + navPort + " is not reachable on " + navIP + 
                "\n\nTroubleshooting:\n" +
                "1. Ensure navigation system is running\n" +
                "2. Check if both devices are on same network\n" +
                "3. Verify IP address: " + navIP + "\n" +
                "4. Check firewall settings");
        }
        
        // Step 3: Check HTTP endpoint
        String navURL = networkConfig.getNavigationURL();
        if (!isHttpEndpointAccessible(navURL)) {
            return new VerificationResult(false, 
                "Navigation web interface is not accessible at " + navURL + 
                "\n\nTroubleshooting:\n" +
                "1. Check if web server is running on navigation system\n" +
                "2. Verify URL: " + navURL + "\n" +
                "3. Test in browser: " + navURL);
        }
        
        // Step 4: Check ROS Bridge (optional)
        if (!isRosBridgeAccessible(navIP, 9090)) {
            Log.w(TAG, "ROS Bridge not accessible, but continuing...");
        }
        
        return new VerificationResult(true, 
            "Navigation system verified successfully\n" +
            "IP: " + navIP + "\n" +
            "Port: " + navPort + "\n" +
            "URL: " + navURL + "\n" +
            "Status: Ready for navigation");
    }
    
    /**
     * Check if a port is reachable
     */
    private boolean isPortReachable(String host, int port, int timeoutMs) {
        try (Socket socket = new Socket()) {
            socket.connect(new InetSocketAddress(host, port), timeoutMs);
            return true;
        } catch (IOException e) {
            Log.w(TAG, "Port " + port + " not reachable on " + host + ": " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Check if HTTP endpoint is accessible
     */
    private boolean isHttpEndpointAccessible(String urlString) {
        try {
            URL url = new URL(urlString);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(5000);
            
            int responseCode = connection.getResponseCode();
            connection.disconnect();
            
            // Accept any response code that indicates server is responding
            boolean accessible = responseCode > 0 && responseCode < 500;
            Log.i(TAG, "HTTP endpoint " + urlString + " returned code: " + responseCode);
            return accessible;
            
        } catch (Exception e) {
            Log.w(TAG, "HTTP endpoint not accessible: " + urlString + " - " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Check if ROS Bridge WebSocket is accessible
     */
    private boolean isRosBridgeAccessible(String host, int port) {
        return isPortReachable(host, port, 3000);
    }
    
    /**
     * Get current connection status without performing new verification
     */
    public boolean isNavigationSystemConnected() {
        return isNavigationSystemConnected;
    }
    
    /**
     * Force refresh of connection status
     */
    public void refreshConnectionStatus(VerificationCallback callback) {
        lastVerificationTime = 0; // Force new verification
        verifyNavigationSystem(callback);
    }
    
    /**
     * Get detailed system information for debugging
     */
    public String getSystemInfo() {
        return "Navigation System Information:\n" +
               "IP: " + networkConfig.getNavigationIP() + "\n" +
               "Port: " + networkConfig.getNavigationPort() + "\n" +
               "URL: " + networkConfig.getNavigationURL() + "\n" +
               "WiFi Connected: " + networkConfig.isWiFiConnected() + "\n" +
               "WiFi SSID: " + networkConfig.getWiFiSSID() + "\n" +
               "Auto Detect: " + networkConfig.isAutoDetectEnabled() + "\n" +
               "Last Verification: " + (lastVerificationTime > 0 ? 
                   new java.util.Date(lastVerificationTime).toString() : "Never") + "\n" +
               "Status: " + (isNavigationSystemConnected ? "Connected" : "Disconnected");
    }
    
    /**
     * Result of verification process
     */
    private static class VerificationResult {
        final boolean isConnected;
        final String message;
        
        VerificationResult(boolean isConnected, String message) {
            this.isConnected = isConnected;
            this.message = message;
        }
    }
    
    /**
     * Clean up resources
     */
    public void cleanup() {
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
    }
}
