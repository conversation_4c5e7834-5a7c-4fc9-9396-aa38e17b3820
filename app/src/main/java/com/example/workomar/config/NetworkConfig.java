package com.example.workomar.config;

import android.content.Context;
import android.content.SharedPreferences;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.text.TextUtils;

import com.reeman.robot.disinfection.utils.SpManager;

/**
 * Network configuration for navigation system communication
 */
public class NetworkConfig {
    
    // Default navigation system settings
    public static final String DEFAULT_NAV_IP = "*************";
    public static final int DEFAULT_NAV_PORT = 5000;
    public static final String DEFAULT_NAV_ENDPOINT = "/pad";
    
    // Configuration keys
    private static final String KEY_NAV_IP = "nav_ip_address";
    private static final String KEY_NAV_PORT = "nav_port";
    private static final String KEY_AUTO_DETECT_IP = "auto_detect_ip";
    
    private static NetworkConfig instance;
    private Context context;
    
    private NetworkConfig(Context context) {
        this.context = context.getApplicationContext();
    }
    
    public static synchronized NetworkConfig getInstance(Context context) {
        if (instance == null) {
            instance = new NetworkConfig(context);
        }
        return instance;
    }
    
    /**
     * Get navigation system IP address
     */
    public String getNavigationIP() {
        String savedIP = SpManager.getInstance().getString(KEY_NAV_IP, null);
        if (!TextUtils.isEmpty(savedIP)) {
            return savedIP;
        }
        
        // Auto-detect IP if enabled
        if (isAutoDetectEnabled()) {
            String detectedIP = detectNavigationIP();
            if (!TextUtils.isEmpty(detectedIP)) {
                return detectedIP;
            }
        }
        
        return DEFAULT_NAV_IP;
    }
    
    /**
     * Set navigation system IP address
     */
    public void setNavigationIP(String ipAddress) {
        SpManager.getInstance().edit()
                .putString(KEY_NAV_IP, ipAddress)
                .apply();
    }
    
    /**
     * Get navigation system port
     */
    public int getNavigationPort() {
        return SpManager.getInstance().getInt(KEY_NAV_PORT, DEFAULT_NAV_PORT);
    }
    
    /**
     * Set navigation system port
     */
    public void setNavigationPort(int port) {
        SpManager.getInstance().edit()
                .putInt(KEY_NAV_PORT, port)
                .apply();
    }
    
    /**
     * Get full navigation URL
     */
    public String getNavigationURL() {
        return String.format("http://%s:%d%s", 
                getNavigationIP(), 
                getNavigationPort(), 
                DEFAULT_NAV_ENDPOINT);
    }
    
    /**
     * Check if auto IP detection is enabled
     */
    public boolean isAutoDetectEnabled() {
        return SpManager.getInstance().getBoolean(KEY_AUTO_DETECT_IP, true);
    }
    
    /**
     * Enable/disable auto IP detection
     */
    public void setAutoDetectEnabled(boolean enabled) {
        SpManager.getInstance().edit()
                .putBoolean(KEY_AUTO_DETECT_IP, enabled)
                .apply();
    }
    
    /**
     * Detect navigation system IP automatically
     */
    private String detectNavigationIP() {
        try {
            WifiManager wifiManager = (WifiManager) context.getSystemService(Context.WIFI_SERVICE);
            if (wifiManager != null && wifiManager.isWifiEnabled()) {
                WifiInfo wifiInfo = wifiManager.getConnectionInfo();
                if (wifiInfo != null) {
                    int ipAddress = wifiInfo.getIpAddress();
                    if (ipAddress != 0) {
                        // Convert to readable IP format
                        String deviceIP = String.format("%d.%d.%d.%d",
                                (ipAddress & 0xff),
                                (ipAddress >> 8 & 0xff),
                                (ipAddress >> 16 & 0xff),
                                (ipAddress >> 24 & 0xff));
                        
                        // Assume navigation system is on same subnet with .180 ending
                        String[] parts = deviceIP.split("\\.");
                        if (parts.length == 4) {
                            return parts[0] + "." + parts[1] + "." + parts[2] + ".180";
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
    
    /**
     * Check if device is connected to network
     */
    public boolean isNetworkConnected() {
        ConnectivityManager cm = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (cm != null) {
            NetworkInfo activeNetwork = cm.getActiveNetworkInfo();
            return activeNetwork != null && activeNetwork.isConnectedOrConnecting();
        }
        return false;
    }
    
    /**
     * Check if device is connected to WiFi
     */
    public boolean isWiFiConnected() {
        ConnectivityManager cm = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (cm != null) {
            NetworkInfo activeNetwork = cm.getActiveNetworkInfo();
            return activeNetwork != null && 
                   activeNetwork.getType() == ConnectivityManager.TYPE_WIFI && 
                   activeNetwork.isConnected();
        }
        return false;
    }
    
    /**
     * Get current WiFi SSID
     */
    public String getWiFiSSID() {
        try {
            WifiManager wifiManager = (WifiManager) context.getSystemService(Context.WIFI_SERVICE);
            if (wifiManager != null && wifiManager.isWifiEnabled()) {
                WifiInfo wifiInfo = wifiManager.getConnectionInfo();
                if (wifiInfo != null) {
                    String ssid = wifiInfo.getSSID();
                    if (ssid != null && ssid.startsWith("\"") && ssid.endsWith("\"")) {
                        return ssid.substring(1, ssid.length() - 1);
                    }
                    return ssid;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
    
    /**
     * Reset network configuration to defaults
     */
    public void resetToDefaults() {
        SpManager.getInstance().edit()
                .remove(KEY_NAV_IP)
                .remove(KEY_NAV_PORT)
                .putBoolean(KEY_AUTO_DETECT_IP, true)
                .apply();
    }
}
