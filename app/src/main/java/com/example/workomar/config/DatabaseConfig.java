package com.example.workomar.config;

import android.content.Context;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.room.Database;
import androidx.room.Room;
import androidx.room.RoomDatabase;
import androidx.room.TypeConverters;
import androidx.sqlite.db.SupportSQLiteDatabase;

import com.example.workomar.BuildConfig;
import com.reeman.robot.disinfection.repository.converter.DateConverter;
import com.reeman.robot.disinfection.repository.dao.TaskDao;
import com.reeman.robot.disinfection.repository.entities.Task;

/**
 * Enhanced database configuration for the workomar package
 */
@Database(entities = {Task.class}, exportSchema = false, version = 2)
@TypeConverters(DateConverter.class)
public abstract class DatabaseConfig extends RoomDatabase {

    private static final String DB_NAME = "workomar_task_db";
    private static final String LEGACY_DB_NAME = "db_task";

    private static DatabaseConfig sInstance;

    public abstract TaskDao taskDao();

    public static DatabaseConfig getInstance(Context context) {
        if (sInstance == null) {
            synchronized (DatabaseConfig.class) {
                if (sInstance == null) {
                    sInstance = createDatabase(context);
                }
            }
        }
        return sInstance;
    }

    private static DatabaseConfig createDatabase(Context context) {
        return Room.databaseBuilder(context, DatabaseConfig.class, DB_NAME)
                .addCallback(new Callback() {
                    @Override
                    public void onCreate(@NonNull SupportSQLiteDatabase db) {
                        Log.w(BuildConfig.TAG, "DatabaseConfig onCreate - New database created");
                        // Initialize any default data here if needed
                    }

                    @Override
                    public void onOpen(@NonNull SupportSQLiteDatabase db) {
                        Log.w(BuildConfig.TAG, "DatabaseConfig onOpen - Database opened successfully");
                    }

                    @Override
                    public void onDestructiveMigration(SupportSQLiteDatabase db) {
                        Log.w(BuildConfig.TAG, "DatabaseConfig onDestructiveMigration - Database migrated");
                    }
                })
                .addMigrations(MIGRATION_1_2)
                .fallbackToDestructiveMigration() // Allow destructive migration if needed
                .build();
    }

    // Migration from version 1 to 2 (package name change)
    private static final androidx.room.migration.Migration MIGRATION_1_2 = 
        new androidx.room.migration.Migration(1, 2) {
            @Override
            public void migrate(SupportSQLiteDatabase database) {
                Log.w(BuildConfig.TAG, "Migrating database from version 1 to 2");
                // No schema changes needed, just version bump for package change
            }
        };

    /**
     * Migrate data from legacy database if it exists
     */
    public static void migrateLegacyData(Context context) {
        // Skip migration for now to avoid circular dependencies
        // Migration will be handled manually if needed
        Log.w(BuildConfig.TAG, "Legacy database migration skipped to avoid initialization issues");
    }

    /**
     * Clear all data (for testing purposes)
     */
    public void clearAllData() {
        try {
            taskDao().deleteAllTasks();
            Log.w(BuildConfig.TAG, "All database data cleared");
        } catch (Exception e) {
            Log.e(BuildConfig.TAG, "Error clearing database data", e);
        }
    }

    /**
     * Get database info for debugging
     */
    public String getDatabaseInfo() {
        try {
            int taskCount = taskDao().getTaskCountSync();
            return "Database: " + DB_NAME + ", Tasks: " + taskCount;
        } catch (Exception e) {
            return "Database info unavailable: " + e.getMessage();
        }
    }

    /**
     * Check database health
     */
    public boolean isDatabaseHealthy() {
        try {
            // Simple query to check if database is accessible
            taskDao().getTaskCountSync();
            return true;
        } catch (Exception e) {
            Log.e(BuildConfig.TAG, "Database health check failed", e);
            return false;
        }
    }
}
