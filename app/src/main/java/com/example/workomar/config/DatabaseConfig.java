package com.example.workomar.config;

import android.content.Context;
import android.util.Log;

import com.example.workomar.BuildConfig;

/**
 * Simple database configuration helper for the workomar package
 * This class provides database configuration without extending RoomDatabase to avoid crashes
 */
public class DatabaseConfig {

    private static final String TAG = "DatabaseConfig";
    private static final String DB_NAME = "workomar_task_db";
    private static final String LEGACY_DB_NAME = "db_task";

    /**
     * Migrate data from legacy database if it exists
     */
    public static void migrateLegacyData(Context context) {
        // Skip migration for now to avoid circular dependencies
        // Migration will be handled manually if needed
        Log.w(TAG, "Legacy database migration skipped to avoid initialization issues");
    }

    /**
     * Clear all data (for testing purposes)
     */
    public void clearAllData() {
        try {
            taskDao().deleteAllTasks();
            Log.w(BuildConfig.TAG, "All database data cleared");
        } catch (Exception e) {
            Log.e(BuildConfig.TAG, "Error clearing database data", e);
        }
    }

    /**
     * Get database info for debugging
     */
    public String getDatabaseInfo() {
        try {
            int taskCount = taskDao().getTaskCountSync();
            return "Database: " + DB_NAME + ", Tasks: " + taskCount;
        } catch (Exception e) {
            return "Database info unavailable: " + e.getMessage();
        }
    }

    /**
     * Check database health
     */
    public boolean isDatabaseHealthy() {
        try {
            // Simple query to check if database is accessible
            taskDao().getTaskCountSync();
            return true;
        } catch (Exception e) {
            Log.e(BuildConfig.TAG, "Database health check failed", e);
            return false;
        }
    }
}
