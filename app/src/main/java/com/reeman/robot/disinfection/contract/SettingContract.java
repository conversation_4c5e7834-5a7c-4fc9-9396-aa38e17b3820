package com.reeman.robot.disinfection.contract;

import android.content.Context;
import android.text.Editable;
import android.widget.EditText;

import com.reeman.robot.disinfection.presenter.IPresenter;
import com.reeman.robot.disinfection.view.IView;
import com.reeman.robot.disinfection.widgets.SetLockScreenPopupWindow;
import com.reeman.robot.disinfection.R;

public interface SettingContract {

    interface Presenter extends IPresenter{

        void persist(Context context, int voiceBroadcast, int lockScreen, int openDetection,String navSpeed, int lowPower, int volume, String delay);

        void onLockScreenConfirmed(SetLockScreenPopupWindow window, EditText editText, String password, boolean isOpenLock);

        void onDismissOperation();

        void onLogoutOrLogInClicked(Context context);

        void onTryListen(Context context, Editable prompt);

        void onSaveDisinfectionPromptAudio(Context context, Editable text);
    }

    interface View extends IView {

        void showInvalidLowPower();

        void onSaveSuccess();

        void onLockScreenInvalid();

        void onLockScreenFirstPasswordConfirmed(EditText editText);

        void onCloseLockScreenPasswordError(EditText editText);

        void onOpenLockScreenSuccess(SetLockScreenPopupWindow window);

        void onLockScreenPasswordNotSame(EditText editText);

        void onCloseLockScreenSuccess(SetLockScreenPopupWindow window);

        void onLockScreenDismiss();

        void onSaveAudioSuccess();

        void onSynthesizeStart();

        void onSynthesizeEnd();

        void showInvalidDelayTime();

        void onLogoutSuccess();

        void onSynthesizeError(String message);
    }
}
