package com.reeman.robot.disinfection.base;

import android.app.Application;
import android.content.Context;
import android.content.Intent;
import android.media.AudioManager;

import com.reeman.robot.disinfection.controller.NavigationController;
import com.reeman.robot.disinfection.plugin.Bugly;
import com.reeman.robot.disinfection.plugin.Log;
import com.reeman.robot.disinfection.receiver.RobotReceiver;
import com.reeman.robot.disinfection.repository.DbRepository;
import com.reeman.robot.disinfection.repository.db.AppDataBase;
import com.reeman.robot.disinfection.service.BackendService;
import com.reeman.robot.disinfection.utils.SpManager;
import com.reeman.robot.disinfection.utils.ToastUtils;
import com.reeman.ros.controller.RobotActionController;
import com.example.workomar.config.DatabaseConfig;

public class BaseApplication extends Application {
    public static BaseApplication mApp;
    public static RobotActionController controller;
    public static NavigationController nc;
    public static DbRepository dbRepository;

    @Override
    public void onCreate() {
        super.onCreate();
        mApp = this;

        //sp存储工具类
        SpManager.init(this);

        //吐司工具类
        ToastUtils.init(this);

        //日志
//        Log.init();

        //在线更新， bug上报
        Bugly.initBugly(this);

        //导航操作
//        controller = RobotActionController.getInstance();

        //数据库操作 - Use legacy database for stability
        try {
            dbRepository = DbRepository.getInstance(AppDataBase.getInstance(this));
            android.util.Log.i("BaseApplication", "Database initialized successfully");
        } catch (Exception e) {
            android.util.Log.e("BaseApplication", "Database initialization failed", e);
            // Create a minimal fallback
            dbRepository = null;
        }

        //启动后台任务
        startService(new Intent(this, BackendService.class));

        registerReceiver(new RobotReceiver(),new RobotReceiver.RobotIntentFilter());
    }


}
