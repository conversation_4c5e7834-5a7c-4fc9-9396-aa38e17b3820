package com.reeman.robot.disinfection.utils;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import com.reeman.robot.disinfection.R;

public class PackageUtils {
    public static String getVersion(Context context) {
        String versionName;
        try {
            PackageInfo packageInfo = context.getPackageManager().getPackageInfo(context.getPackageName(), 0);
            versionName = packageInfo.versionName;
        } catch (PackageManager.NameNotFoundException e) {
            versionName = "1.0.0";
        }
        return versionName;
    }
}
