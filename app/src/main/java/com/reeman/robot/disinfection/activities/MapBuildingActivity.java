package com.reeman.robot.disinfection.activities;

import android.app.Dialog;
import android.content.SharedPreferences;
import android.graphics.Bitmap;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebView;
import android.widget.FrameLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.elvishew.xlog.XLog;
import com.example.workomar.R;
import com.reeman.robot.disinfection.base.BaseActivity;
import com.reeman.robot.disinfection.constants.Constant;
import com.reeman.robot.disinfection.contract.MapBuildingContract;
import com.reeman.robot.disinfection.event.RobotEvent;
import com.reeman.robot.disinfection.presenter.impl.MapBuildingPresenter;
import com.reeman.robot.disinfection.utils.SpManager;
import com.reeman.robot.disinfection.utils.ToastUtils;
import com.reeman.robot.disinfection.utils.VoiceHelper;
import com.reeman.robot.disinfection.widgets.EasyDialog;
import com.reeman.robot.disinfection.widgets.MapWebViewChromeClient;
import com.reeman.robot.disinfection.widgets.MapWebViewClient;
import com.reeman.robot.disinfection.widgets.WebViewHolder;
import com.reeman.ros.event.Event;
import com.example.workomar.config.NetworkConfig;
import com.example.workomar.verification.NavigationSystemVerifier;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import static com.reeman.robot.disinfection.base.BaseApplication.controller;


public class MapBuildingActivity extends BaseActivity implements View.OnClickListener, MapBuildingContract.View, MapWebViewClient.OnMapWebViewEventListener {

    private TextView tvSetChargingPile;
    private TextView tvSetDisinfectionPoint;
    private TextView tvSaveMap;
    private TextView tvExitDeploy;
    private TextView tvAbandonMapBuilding;
    private TextView tvDisinfectionPointTest;
    private MapBuildingPresenter presenter;
    private WebView mMapWebView;
    private RelativeLayout llOperationBar;
    private TextView tvHostname;
    private TextView tvHostIp;
    private TextView tvWiFi;
    private boolean isFirstEnter = true;
    private FrameLayout flWebLayout;
    private boolean requestModeMyself = false;
    private String url;
    private NetworkConfig networkConfig;

    private boolean isReceiveError = false;

    private final Runnable timeoutRunnable = new Runnable() {
        @Override
        public void run() {
            if (EasyDialog.isShow()) EasyDialog.getInstance().dismiss();
            EasyDialog.getInstance(MapBuildingActivity.this).warn(getString(R.string.text_page_load_failed), new EasyDialog.OnViewClickListener() {
                @Override
                public void onViewClick(Dialog dialog, int id) {
                    if (id == R.id.btn_confirm) {
                        dialog.dismiss();
                        finish();
                    }
                }
            });
        }
    };

    private final Runnable navModeRunnable = new Runnable() {
        @Override
        public void run() {
            navModeQueryCount++;
            XLog.d("Navigation mode query attempt: " + navModeQueryCount);

            if (navModeQueryCount >= 5) { // Increased retry count
                navModeQueryCount = 0;
                XLog.e("Navigation mode query failed after 5 attempts");

                EasyDialog.getInstance(MapBuildingActivity.this).warn(
                    getString(R.string.text_communicate_failed_with_ros) + "\n\nTroubleshooting:\n" +
                    "1. Check if navigation system is running\n" +
                    "2. Verify network connection\n" +
                    "3. Check IP address: " + networkConfig.getNavigationIP(),
                    new EasyDialog.OnViewClickListener() {
                        @Override
                        public void onViewClick(Dialog dialog, int id) {
                            dialog.dismiss();
                            // Offer retry option
                            retryNavigationConnection();
                        }
                    });
                return;
            }

            try {
                requestModeMyself = true;
                controller.modelRequest();
                mHandler.postDelayed(this, 1000); // Increased delay to 1 second
            } catch (Exception e) {
                XLog.e("Error during navigation mode request", e);
                mHandler.postDelayed(this, 1000);
            }
        }
    };
    private int navModeQueryCount = 0;

    private void retryNavigationConnection() {
        navModeQueryCount = 0;
        EasyDialog.getInstance(this).confirm("Do you want to retry connecting to the navigation system?",
            new EasyDialog.OnViewClickListener() {
                @Override
                public void onViewClick(Dialog dialog, int id) {
                    dialog.dismiss();
                    if (id == R.id.btn_confirm) {
                        // Reset and retry
                        initNavigationCommunication();
                    } else {
                        finish();
                    }
                }
            });
    }

    @Override
    protected int getLayoutRes() {
        return R.layout.activity_map_building;
    }

    @Override
    protected void initView() {
        TextView tvRebuildMap = $(R.id.tv_rebuild_map);
        tvRebuildMap.setOnClickListener(this);

        tvSetChargingPile = $(R.id.tv_set_charging_pile);
        tvSetChargingPile.setOnClickListener(this);

        tvSetDisinfectionPoint = $(R.id.tv_set_disinfection_point);
        tvSetDisinfectionPoint.setOnClickListener(this);

        tvDisinfectionPointTest = $(R.id.tv_disinfection_route_test);
        tvDisinfectionPointTest.setOnClickListener(this);

        tvSaveMap = $(R.id.tv_save_map);
        tvSaveMap.setOnClickListener(this);

        tvAbandonMapBuilding = $(R.id.tv_back_to_main_page);
        tvAbandonMapBuilding.setOnClickListener(this);

        tvExitDeploy = $(R.id.tv_exit_deploy);
        tvExitDeploy.setOnClickListener(this);

        llOperationBar = $(R.id.ll_operation_bar);

        tvHostname = $(R.id.tv_host_name);
        tvHostIp = $(R.id.tv_host_ip);
        tvWiFi = $(R.id.tv_wifi);

        flWebLayout = $(R.id.web_layout);
        mMapWebView = WebViewHolder.getView(this);
        mMapWebView.setWebChromeClient(new MapWebViewChromeClient());
        mMapWebView.setWebViewClient(new MapWebViewClient(this));
        flWebLayout.addView(mMapWebView, 0);
    }

    @Override
    protected void initData() {
        presenter = new MapBuildingPresenter(this);
        networkConfig = NetworkConfig.getInstance(this);
        controller.positionAutoUploadControl(false);
    }

    @Override
    protected void onResume() {
        super.onResume();
        WebViewHolder.onResume();

        // Check network connectivity first
        if (!networkConfig.isNetworkConnected()) {
            EasyDialog.getInstance(this).warn("No network connection. Please check your network settings.", new EasyDialog.OnViewClickListener() {
                @Override
                public void onViewClick(Dialog dialog, int id) {
                    dialog.dismiss();
                    finish();
                }
            });
            return;
        }

        String ipAddress = networkConfig.getNavigationIP();
        XLog.d("Navigation IP: " + ipAddress);

        if ("127.0.0.1".equals(ipAddress)) {
            EasyDialog.getInstance(this).warn(getString(R.string.text_android_ros_same_network), new EasyDialog.OnViewClickListener() {
                @Override
                public void onViewClick(Dialog dialog, int id) {
                    dialog.dismiss();
                    finish();
                }
            });
            return;
        }

        // Initialize navigation communication with retry mechanism
        initNavigationCommunication();
    }

    private void initNavigationCommunication() {
        try {
            //获取一些导航信息
            controller.getHostName();
            controller.getHostIp();
            mHandler.postDelayed(navModeRunnable, 300);
        } catch (Exception e) {
            XLog.e("Failed to initialize navigation communication", e);
            EasyDialog.getInstance(this).warn(getString(R.string.text_communicate_failed_with_ros), new EasyDialog.OnViewClickListener() {
                @Override
                public void onViewClick(Dialog dialog, int id) {
                    dialog.dismiss();
                    finish();
                }
            });
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        WebViewHolder.onPause();
        if (VoiceHelper.isPlaying()) {
            VoiceHelper.pause();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mMapWebView != null) {
            mMapWebView.clearHistory();
            mMapWebView.clearFormData();
            mMapWebView.stopLoading();
            flWebLayout.removeView(mMapWebView);
            WebViewHolder.destroy();
            mMapWebView.setWebChromeClient(null);
            mMapWebView.setWebViewClient(null);
        }
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        switch (id) {
            case R.id.tv_rebuild_map:
                presenter.onRebuildMapClicked(this);
                break;
            case R.id.tv_set_disinfection_point:
            case R.id.tv_set_charging_pile:
                presenter.onMarkPointClicked(this, id);
                break;
            case R.id.tv_disinfection_route_test:
                presenter.onDisinfectionRouteTestClicked(this);
                break;
            case R.id.tv_save_map:
                presenter.onSaveMapClicked(this);
                break;
            case R.id.tv_back_to_main_page:
                presenter.onAbandonMapBuildingClicked(this);
                break;
            case R.id.tv_exit_deploy:
                presenter.onExitDeployClicked(this);
                break;
        }
    }

    @Override
    protected void onSensorsError(Event.OnCheckSensorsEvent event) {
        super.onSensorsError(event);

    }

    @Override
    protected void onCustomEmergencyStopStateChange(int emergencyStopState) {
        presenter.onEmergencyStopStateChange(this, emergencyStopState);
    }

    @Override
    protected void onNavigationCompleteResult(int code, String name, float mileage) {
        presenter.onNavigationCompleteResult(this,code, name, mileage);
    }

    @Override
    protected void onNavigationStartResult(int code, String name) {
        if (code == 0){
            presenter.onPointFound(this);
        }else {
            presenter.onNavigationStartResult(this,code, name);
        }
    }

    @Override
    protected void onCustomPowerDisconnected() {

    }

    @Override
    protected void onCustomPowerConnected() {
        presenter.onPowerConnected(this);
    }

    @Override
    protected void onCustomDockFailed() {
        presenter.onDockFailed(this);
    }

    /**
     * 主机名加载完成
     *
     * @param event
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onHostnameObtained(Event.OnHostnameEvent event) {
        tvHostname.setText(getString(R.string.text_current_host_name, event.hostname));
    }

    /**
     * 获取到导航模式
     *
     * @param event
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onNavModelObtained(Event.OnNavModeEvent event) {
        if (!requestModeMyself)return;
        requestModeMyself = false;
        mHandler.removeCallbacks(navModeRunnable);
        mHandler.postDelayed(timeoutRunnable, 40_000);
        boolean hasGuide = SpManager.getInstance().getBoolean(Constant.HAS_GUIDE_BUILD_MAP, false);
        if (!hasGuide) {
            SharedPreferences.Editor edit = SpManager.getInstance().edit();
            edit.putBoolean(Constant.HAS_GUIDE_BUILD_MAP, true);
            edit.apply();
            presenter.setBackFromMapScanning(true);
            if (event.mode != 2)
                presenter.changeToConstructMap();
            showCancelableLoading(getString(R.string.voice_entering_map_building_mode));
            VoiceHelper.play("voice_entering_map_building_mode");
            return;
        }

        presenter.setCurrentMode(event.mode);
        if (event.mode == 2 || event.mode == 3) {
            showCancelableLoading(getString(R.string.voice_entering_map_building_mode));
            VoiceHelper.play("voice_entering_map_building_mode");
        }else {
            showCancelableLoading(getString(R.string.voice_entering_nav_mode));
            VoiceHelper.play("voice_entering_nav_mode");
        }
    }

    /**
     * 导航WIFI获取完成
     *
     * @param event
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onHostWIFIObtained(Event.OnIpEvent event) {
        tvWiFi.setText(getString(R.string.text_ros_wifi, event.wifiName));
        tvHostIp.setText(getString(R.string.text_ros_ip, event.ipAddress));

        // Use dynamic URL from network configuration
        url = networkConfig.getNavigationURL();
        XLog.d("Loading navigation URL: " + url);

        // Load URL with error handling
        try {
            mMapWebView.loadUrl(url);
        } catch (Exception e) {
            XLog.e("Failed to load navigation URL", e);
            EasyDialog.getInstance(this).warn("Failed to load navigation interface. Please check network connection.", new EasyDialog.OnViewClickListener() {
                @Override
                public void onViewClick(Dialog dialog, int id) {
                    dialog.dismiss();
                    // Retry loading
                    mMapWebView.reload();
                }
            });
        }
    }

    /**
     * 重定位完成
     *
     * @param event
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onInitPose(Event.OnInitPoseEvent event) {
        XLog.w("收到initPose");
        if (EasyDialog.isShow()) {
            EasyDialog.getInstance().dismiss();
        }
    }

    /**
     * 位置加载完成
     *
     * @param event
     */
    @Subscribe
    public void onPositionObtained(Event.OnPositionEvent event) {
        presenter.onMarkPoint(this, event.position);
    }


    /**
     * 目标点位标注成功
     *
     * @param event
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onMarkSuccess(Event.OnSetFlagPointEvent event) {
        if (event.result == 0) {
            ToastUtils.showShortToast(getString(R.string.voice_target_point_mark_success));
            VoiceHelper.play("voice_target_point_mark_success");
            if (presenter.getPointType() == 1) presenter.addPointCount();
        }else {
            if (event.result == -3) {
                EasyDialog.getInstance(this).confirm(getString(R.string.text_point_exist), (dialog, id) -> {
                    if (id == R.id.btn_confirm) {
                        presenter.deletePoint();
                    }
                    dialog.dismiss();
                });
                return;
            }
            ToastUtils.showShortToast(getString(R.string.voice_target_point_mark_failed));
            VoiceHelper.play("voice_target_point_mark_failed");
        }
    }

    @Subscribe
    public void onDelPoint(Event.OnDelFlagPointEvent event){
        controller.getCurrentPosition();
    }

    @Override
    public void onEnteringMapBuildingMode(String prompt) {
        showLoading(prompt);
        mMapWebView.reload();
    }

    @Override
    public void onEnteringNavMode(String prompt) {
        showLoading(prompt);
        mMapWebView.reload();
    }

    @Override
    public void onConfirmExitDeploy() {
        finish();
    }


    @Override
    public void onRouteTestProgressUpdate(String prompt) {
        EasyDialog.getInstance().update(R.id.tv_content, prompt);
    }

    /**
     * 显示加载框
     *
     * @param prompt
     */
    private void showLoading(String prompt) {
        EasyDialog.getCancelableLoadingInstance(this).loadingCancelable(prompt, (dialog, id) -> {
            dialog.dismiss();
            finish();
        });
    }

    public void showCancelableLoading(String prompt) {
        EasyDialog.getCancelableLoadingInstance(this).loadingCancelable(prompt, (dialog, id) -> {
            dialog.dismiss();
            mHandler.removeCallbacks(timeoutRunnable);
            mHandler.removeCallbacks(navModeRunnable);
            finish();
        });
    }

    /**
     * 网页加载开始
     *
     * @param view
     * @param url
     * @param favicon
     */
    @Override
    public void onPageStart(WebView view, String url, Bitmap favicon) {

    }

    /**
     * 网页加载完成
     *
     * @param view
     * @param url
     */
    @Override
    public void onPageFinished(WebView view, String url) {

        mHandler.removeCallbacks(timeoutRunnable);

        FrameLayout.MarginLayoutParams layoutParams = (FrameLayout.MarginLayoutParams) mMapWebView.getLayoutParams();
        layoutParams.leftMargin = -330;
        mMapWebView.setLayoutParams(layoutParams);
        llOperationBar.setVisibility(View.VISIBLE);

        if (presenter.getCurrentNavMode() == 2) {
            if (EasyDialog.isShow()) EasyDialog.getInstance().dismiss();
            VoiceHelper.play("voice_current_in_map_building_mode");
            tvHostname.setVisibility(View.INVISIBLE);
            tvHostIp.setVisibility(View.INVISIBLE);
            tvWiFi.setVisibility(View.INVISIBLE);
            tvSetDisinfectionPoint.setVisibility(View.GONE);
            tvSetChargingPile.setVisibility(View.GONE);
            tvDisinfectionPointTest.setVisibility(View.GONE);
            tvSaveMap.setVisibility(View.VISIBLE);
            tvAbandonMapBuilding.setVisibility(View.VISIBLE);
            tvExitDeploy.setVisibility(View.GONE);
        } else if (presenter.getCurrentNavMode() == 1) {
            VoiceHelper.play("voice_current_in_nav_mode");
            if (isFirstEnter && !presenter.isBackFromMapScanning()) {
                isFirstEnter = false;
                if (EasyDialog.isShow()) EasyDialog.getInstance().dismiss();
            }
            tvHostname.setVisibility(View.VISIBLE);
            tvHostIp.setVisibility(View.VISIBLE);
            tvWiFi.setVisibility(View.VISIBLE);
            tvSetDisinfectionPoint.setVisibility(View.VISIBLE);
            tvSetChargingPile.setVisibility(View.VISIBLE);
            tvDisinfectionPointTest.setVisibility(View.VISIBLE);
            tvExitDeploy.setVisibility(View.VISIBLE);
            tvSaveMap.setVisibility(View.GONE);
            tvAbandonMapBuilding.setVisibility(View.GONE);
        }
    }


    @Override
    public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
        if (!isReceiveError) {
            isReceiveError = true;
            mHandler.removeCallbacks(navModeRunnable);
            mHandler.removeCallbacks(timeoutRunnable);
            timeoutRunnable.run();
        }
    }
}
