package com.reeman.robot.disinfection.utils;

import android.content.res.AssetFileDescriptor;
import android.media.AudioManager;
import android.media.MediaPlayer;
import android.os.Handler;
import android.os.Looper;

import com.reeman.robot.disinfection.constants.Constant;

import java.io.IOException;
import java.util.Locale;

import static com.reeman.robot.disinfection.base.BaseApplication.mApp;
import com.reeman.robot.disinfection.R;


public class VoiceHelper {

    private static MediaPlayer mediaPlayer;

    public static void play(String name) {
        play(name, () -> {
        });
    }

    public static void play(String name, OnCompleteListener listener) {
        try {
            String path;
            int languageType = SpManager.getInstance().getInt(Constant.KEY_LANGUAGE_TYPE, Constant.DEFAULT_LANGUAGE_TYPE);
            if (languageType != -1) {
                path = LocaleUtil.getAssetsPathByLanguage(languageType);
            } else {
                path = Locale.getDefault().getLanguage() + "/";
            }
            AssetFileDescriptor assetFileDescriptor = mApp.getAssets().openFd(path + name + ".wav");
            if (mediaPlayer == null) {
                mediaPlayer = new MediaPlayer();
                mediaPlayer.setAudioStreamType(AudioManager.STREAM_MUSIC);
            }
            int volume = SpManager.getInstance().getInt(Constant.SYS_VOLUME, Constant.DEFAULT_MEDIA_VOLUME);
            mediaPlayer.setVolume(volume / 15.0f, volume / 15.0f);
            mediaPlayer.reset();
            mediaPlayer.setDataSource(assetFileDescriptor.getFileDescriptor(), assetFileDescriptor.getStartOffset(), assetFileDescriptor.getLength());
            mediaPlayer.setOnCompletionListener(mp -> {
                if (listener == null) return;
                listener.onComplete();
            });
            mediaPlayer.prepare();
            mediaPlayer.start();
        } catch (Exception e) {
            e.printStackTrace();
            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                if (listener == null) return;
                listener.onComplete();
            }, 1500);
        }
    }

    public static boolean isPlaying() {
        try {
            return mediaPlayer != null && mediaPlayer.isPlaying();
        } catch (Exception e) {
            return false;
        }
    }

    public static void pause() {
        try {
            mediaPlayer.pause();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void playFile(String path) {
        try {
            if (mediaPlayer == null) {
                mediaPlayer = new MediaPlayer();
                mediaPlayer.setAudioStreamType(AudioManager.STREAM_MUSIC);
            }
            int volume = SpManager.getInstance().getInt(Constant.SYS_VOLUME, Constant.DEFAULT_MEDIA_VOLUME);
            mediaPlayer.setVolume(volume / 15.0f, volume / 15.0f);
            mediaPlayer.reset();
            mediaPlayer.setDataSource(path);
            mediaPlayer.prepare();
            mediaPlayer.start();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public interface OnCompleteListener {
        void onComplete();
    }
}
