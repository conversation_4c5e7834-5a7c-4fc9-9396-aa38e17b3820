package com.reeman.robot.disinfection.activities;

import android.app.Dialog;
import android.content.Intent;
import android.content.SharedPreferences;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewTreeObserver;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.drawerlayout.widget.DrawerLayout;

import com.example.workomar.R;
import com.reeman.robot.disinfection.base.BaseActivity;
import com.reeman.robot.disinfection.constants.Constant;
import com.reeman.robot.disinfection.constants.Errors;
import com.reeman.robot.disinfection.contract.MainContract;
import com.reeman.robot.disinfection.event.RobotEvent;
import com.reeman.robot.disinfection.presenter.impl.MainPresenter;
import com.reeman.robot.disinfection.proxy.MainPresenterHandler;
import com.reeman.robot.disinfection.repository.entities.Task;
import com.reeman.robot.disinfection.request.model.MapVO;
import com.reeman.robot.disinfection.request.model.TaskModel;
import com.reeman.robot.disinfection.utils.ScreenUtils;
import com.reeman.robot.disinfection.utils.SpManager;
import com.reeman.robot.disinfection.utils.TimeUtils;
import com.reeman.robot.disinfection.utils.ToastUtils;
import com.reeman.robot.disinfection.utils.VoiceHelper;
import com.reeman.robot.disinfection.utils.WIFIUtils;
import com.reeman.robot.disinfection.widgets.ContentViewData;
import com.reeman.robot.disinfection.widgets.EasyDialog;
import com.reeman.robot.disinfection.widgets.GuideItem;
import com.reeman.robot.disinfection.widgets.GuideView;
import com.reeman.robot.disinfection.widgets.LayoutIdData;
import com.reeman.robot.disinfection.widgets.MapChooseDialog;
import com.reeman.robot.disinfection.widgets.OnViewData;
import com.reeman.robot.disinfection.widgets.ScreenUnlockDialog;
import com.reeman.robot.disinfection.widgets.ViewBuilder;
import com.reeman.ros.event.Event;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.util.Arrays;
import java.util.List;

import static com.reeman.robot.disinfection.base.BaseApplication.controller;
import static com.reeman.robot.disinfection.base.BaseApplication.nc;

public class MainActivity extends BaseActivity implements View.OnClickListener, MainContract.View, ScreenUnlockDialog.OnScreenUnlockEventListener, MapChooseDialog.OnMapListItemSelectedListener {

    private TextView tvHostName;
    private TextView tvHostVersion;
    private TextView tvNavigationWIFI;
    private TextView tvNavigationIP;
    private TextView tvCurrentMap;
    private TextView tvTaskMode;
    private TextView tvBackAction;
    private TextView tvDisinfectionMode;
    private TextView tvStayTime;
    private TextView tvDurationTime;
    private TextView tvWifiState;
    private RelativeLayout rlRecentTask;
    private DrawerLayout dlRoot;
    private TextView tvLock;
    private List<GuideItem> guideViews;
    private TextView tvCharge;
    private Button btnLookupRecentTask;
    private Button btnStartDisinfection;
    private LinearLayout llManualTask;
    private long lastClickTimeMills;
    private int fastClickCount = 0;
    private MainContract.Presenter presenter;
    private ScreenUnlockDialog screenLockWindow;
    private MapChooseDialog mapChooseDialog;
    private ImageView ivCharging;

    @Override
    protected int getLayoutRes() {
        return R.layout.activity_main;
    }


    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        if (hasFocus) {
            ScreenUtils.hideBottomUIMenu(this);
            fastClickCount = 0;
        }
    }

    @Override
    protected boolean shouldResponse2RemoteTaskEvent() {
        return true;
    }

    @Override
    protected boolean shouldResponse2TimeEvent() {
        return true;
    }

    @Override
    protected void initView() {
        dlRoot = $(R.id.dl_root);

        ivCharging = $(R.id.iv_charging);

        tvHostName = $(R.id.tv_hostname);
        tvHostName.setOnClickListener(this);

        tvLock = $(R.id.tv_lock);
        tvLock.setOnClickListener(this);

        tvCharge = $(R.id.tv_charge);
        tvCharge.setOnClickListener(this);

        tvWifiState = $(R.id.tv_wifi_state);
        tvWifiState.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (System.currentTimeMillis() - lastClickTimeMills < 300) {
                    if (++fastClickCount >= 1) {
                        EasyDialog.getInstance(MainActivity.this).confirm(getString(R.string.text_exit_app), (dialog, id) -> {
                            if (id == R.id.btn_confirm) {
                                finishAll();
                            }
                            dialog.dismiss();
                        });
                        fastClickCount = 0;
                    }
                }
                lastClickTimeMills = System.currentTimeMillis();
            }
        });

        tvHostVersion = $(R.id.tv_navigation_version);
        tvNavigationWIFI = $(R.id.tv_navigation_wifi);
        tvNavigationIP = $(R.id.tv_navigation_ip);
        tvCurrentMap = $(R.id.tv_current_map);

        llManualTask = $(R.id.ll_manual_task);
        llManualTask.setOnClickListener(this);

        //手动任务
        tvTaskMode = $(R.id.tv_task_mode);
        tvBackAction = $(R.id.tv_back_action);
        tvDisinfectionMode = $(R.id.tv_disinfection_mode);
        tvStayTime = $(R.id.tv_stay_time);
        tvDurationTime = $(R.id.tv_duration_time);

        //最近任务
        rlRecentTask = $(R.id.rl_recent_scheduled_task);
        rlRecentTask.setOnClickListener(this);

        btnLookupRecentTask = $(R.id.btn_look_up_scheduled_task);
        btnLookupRecentTask.setOnClickListener(this);
        TextView tvTitleRecentTask = $(R.id.tv_title_recent_task);
        tvTitleRecentTask.setOnClickListener(this);

        Button btnBuildMap = $(R.id.btn_build_map);
        btnBuildMap.setOnClickListener(this);

        Button btnChooseMap = $(R.id.btn_choose_map);
        btnChooseMap.setOnClickListener(this);

        Button btnChooseWiFi = $(R.id.btn_choose_wifi);
        btnChooseWiFi.setOnClickListener(this);

        btnStartDisinfection = $(R.id.btn_start_disinfection);
        btnStartDisinfection.setOnClickListener(this);
    }

    @Override
    protected void initData() {
        MainPresenter mainPresenter = new MainPresenter(this);
        MainPresenterHandler mainPresenterHandler = new MainPresenterHandler(mainPresenter);
        //生成代理对象，AOP思想
        presenter = (MainContract.Presenter) Proxy.newProxyInstance(
                mainPresenterHandler.getClass().getClassLoader(),
                mainPresenter.getClass().getInterfaces(),
                mainPresenterHandler);
        mHandler.postDelayed(this::onWaypointUpdate, 1000);
    }

    @Override
    protected void onResume() {
        super.onResume();
        llManualTask.setClickable(true);
        //刷新电量状态
        refreshPowerState();
        //急停状态
        refreshEmergencyStopState(nc.getEmergencyStop());
        //锁屏状态
        refreshLockScreenState();
        //网络状态
        refreshNetworkState();

        //加载一些导航信息
        controller.getHostName();
        controller.getHostIp();
        controller.getCurrentMap();
        controller.humanDetectionControl(false);

        //获取任务
        presenter.getManualTask();
        presenter.getMostRecentTask();

        tvHostVersion.setText(Event.getOnHflsVersionEvent().softVersion);

        checkGuide();
    }

    @Override
    protected void onPause() {
        super.onPause();
        llManualTask.setClickable(false);
    }

    /**
     * 检查是否引导过
     */
    private void checkGuide() {
        boolean hasGuideBuildMap = SpManager.getInstance().getBoolean(Constant.HAS_GUIDE_BUILD_MAP, false);
        if (!hasGuideBuildMap) {
            VoiceHelper.play("voice_not_guide_build_map");
            EasyDialog.getInstance(this).confirm(getString(R.string.voice_not_guide_build_map), new EasyDialog.OnViewClickListener() {
                @Override
                public void onViewClick(Dialog dialog, int id) {
                    dialog.dismiss();
                    if (id == R.id.btn_confirm) {
                        presenter.onGotoMapBuildingPage(MainActivity.this);
                    } else if (id == R.id.btn_cancel) {
                        SpManager.getInstance().edit().putBoolean(Constant.HAS_GUIDE_BUILD_MAP, true).apply();
                        startOperationGuide();
                    }
                }
            });
            return;
        }
        startOperationGuide();
    }

    private void startOperationGuide() {
        currentGuideCount = SpManager.getInstance().getInt(Constant.CURRENT_GUIDE_OPERATION_STEP, 0);
        if (currentGuideCount < 7) {
            initGuideView();
            startGuide();
        }
    }

    private int currentGuideCount = 0;

    private void initGuideView() {
        String nextStep = getString(R.string.text_next_step);
        guideViews = Arrays.asList(
                new GuideItem(tvHostName, "voice_guide_goto_setting", getString(R.string.voice_guide_goto_setting), nextStep, LayoutIdData.BOTTOM, 560, 160),
                new GuideItem(tvCharge, "voice_guide_goto_charge", getString(R.string.voice_guide_goto_charge), nextStep, LayoutIdData.RIGHT, 400, 200),
                new GuideItem(btnLookupRecentTask, "voice_guide_look_up_recent_task", getString(R.string.voice_guide_look_up_recent_task), nextStep, LayoutIdData.RIGHT, 360, 200),
                new GuideItem(tvWifiState, "voice_guide_exit_app", getString(R.string.voice_guide_exit_app), nextStep, LayoutIdData.BOTTOM, 600, 200),
                new GuideItem(llManualTask, "voice_guide_goto_manual_task", getString(R.string.voice_guide_goto_manual_task), nextStep, LayoutIdData.LEFT, 400, 200),
                new GuideItem(btnStartDisinfection, "voice_guide_start_disinfection", getString(R.string.voice_guide_start_disinfection), nextStep, LayoutIdData.LEFT, 400, 200),
                new GuideItem($(R.id.ll_drawer), "voice_guide_more_actions", getString(R.string.voice_guide_more_actions), getString(R.string.text_finish), LayoutIdData.LEFT, 500, 300, Gravity.CENTER_HORIZONTAL | Gravity.BOTTOM));
    }


    private void startGuide() {
        mHandler.postDelayed(() -> {
            if (currentGuideCount < guideViews.size()) {
                startGuide(guideViews.get(currentGuideCount), () -> {
                    currentGuideCount++;
                    if (currentGuideCount == guideViews.size() - 1) {
                        dlRoot.openDrawer(Gravity.RIGHT);
                    } else if (currentGuideCount == guideViews.size()) {
                        dlRoot.closeDrawer(Gravity.RIGHT);
                    }
                    startGuide();
                });
            } else {
                SharedPreferences.Editor edit = SpManager.getInstance().edit();
                edit.putInt(Constant.CURRENT_GUIDE_OPERATION_STEP, currentGuideCount);
                edit.apply();
                VoiceHelper.play("voice_guide_finish");
                EasyDialog
                        .getInstance(MainActivity.this)
                        .warn(getString(R.string.voice_guide_finish),
                                new EasyDialog.OnViewClickListener() {
                                    @Override
                                    public void onViewClick(Dialog dialog, int id) {
                                        dialog.dismiss();
                                    }
                                });
            }
        }, 100);
    }

    private void startGuide(GuideItem guideItem, MainActivity.OnSkipListener listener) {
        VoiceHelper.play(guideItem.audioFile);
        GuideView.with(this)
                .setShadowSize(10)
                .setShapeType(GuideView.RECTANGLE)
                .setSkipButton(guideItem.skipText, guideItem.skipGravity)
                .setOnViews(new OnViewData[]{GuideView.buildOnViewData(guideItem.view, 5, 10, 5, 10, GuideView.buildExplainView(guideItem.alignment, new ContentViewData(guideItem.desc), guideItem.width, guideItem.height))})
                .setDismissCallback(new ViewBuilder.DismissCallback() {
                    @Override
                    public void skip() {
                        if (listener != null) {
                            listener.onSkip();
                        }
                    }

                    @Override
                    public void dismiss(int oldPosition, int newPosition) {
                    }
                })
                .show();
    }


    public interface OnSkipListener {
        void onSkip();
    }

    private void refreshNetworkState() {
        String connectWifiSSID = WIFIUtils.getConnectWifiSSID(this);
        if (connectWifiSSID == null || "<unknown ssid>".equals(connectWifiSSID) || "0x".equals(connectWifiSSID)) {
            tvWifiState.setText(getString(R.string.text_wifi_not_connect));
        } else {
            tvWifiState.setText(connectWifiSSID);
        }
    }


    //主机名加载完成
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onHostnameObtained(Event.OnHostnameEvent event) {
        tvHostName.setText(event.hostname);
    }

    //Android网络变化
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onNetworkStateChange(RobotEvent.OnNetworkEvent event) {
        refreshNetworkState();
    }

    //导航WIFI获取完成
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onHostWIFIObtained(Event.OnIpEvent event) {
        tvNavigationWIFI.setText(event.wifiName);
        tvNavigationIP.setText(event.ipAddress);
    }

    //地图加载完成
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onMapObtained(Event.OnMapEvent event) {
        tvCurrentMap.setText(TextUtils.isEmpty(event.alias) ? event.map : event.alias);
    }


    @Override
    protected void onCustomEmergencyStopStateChange(int emergencyStopState) {
        super.onCustomEmergencyStopStateChange(emergencyStopState);
        refreshEmergencyStopState(emergencyStopState);
    }

    @Override
    protected void onCustomStartTaskEvent(TaskModel detail) {
        if (nc.isChargingDocking() ||
                nc.isNavigating() ||
                nc.isAcCharging() ||
                nc.getEmergencyStop() == 0)
            return;
        if (EasyDialog.isShow()) EasyDialog.getInstance().dismiss();
        if (screenLockWindow != null && screenLockWindow.isShowing()) screenLockWindow.dismiss();
        VoiceHelper.play("voice_going_to_start_disinfection_task");
        Intent intent = new Intent(this, TaskExecutingActivity.class);
        intent.putExtra("extra", Task.getByRemoteCommand(detail));
        startActivity(intent);
    }

    //更新急停状态
    private void refreshEmergencyStopState(int state) {
        TextView tvEmergency = $(R.id.tv_emergency_stop_state);
        if (state == 0) {
            tvEmergency.setVisibility(View.VISIBLE);
        } else {
            tvEmergency.setVisibility(View.GONE);
        }
    }

    //刷新锁屏状态
    private void refreshLockScreenState() {
        tvLock.setVisibility(SpManager.getInstance().getBoolean(Constant.LOCK_SCREEN, Constant.DEFAULT_LOCK_SCREEN) ? View.VISIBLE : View.INVISIBLE);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onApplyMapEvent(Event.OnApplyMapEvent event) {
        if (EasyDialog.isShow()) EasyDialog.getInstance().dismiss();
        if (event.success) {
            ToastUtils.showShortToast(getString(R.string.text_change_map_success));
            tvCurrentMap.setText(TextUtils.isEmpty(event.alias) ? event.map : event.alias);
        } else {
            ToastUtils.showShortToast(getString(R.string.text_change_map_failure));
        }
    }


    @Override
    protected void onStartNavFailed(int code) {
        super.onStartNavFailed(code);
        if (EasyDialog.isShow()) {
            EasyDialog.getInstance().dismiss();
        }
        mHandler.postDelayed(() -> {
            String startNaviErrorByCode = Errors.getNavigationStartError(MainActivity.this, code);
            if (!TextUtils.isEmpty(startNaviErrorByCode))
                EasyDialog.getInstance(this).warnError(startNaviErrorByCode);
        }, 1000);
    }


    @Override
    protected void onCustomPowerConnected() {
        //VoiceHelper.play("voice_start_charging");
        if (EasyDialog.isShow()) EasyDialog.getInstance().dismiss();
        ivCharging.setVisibility(View.VISIBLE);
    }

    @Override
    protected void onCustomPowerDisconnected() {
        //VoiceHelper.play("voice_stop_charge");
        ivCharging.setVisibility(View.GONE);
    }

    @Override
    protected void onCustomBatteryChange() {
        refreshPowerState();
    }

    //刷新电量图标
    private void refreshPowerState() {
        int level = Event.getCoreData().battery;
        int plug = Event.getCoreData().charger;
        TextView tvPower = $(R.id.tv_power);
        TextView tvChargeState = $(R.id.tv_charge_state);
        if (plug == 2 || plug == 3) {
            //充电中
            tvChargeState.setVisibility(View.VISIBLE);
            tvChargeState.setCompoundDrawablesWithIntrinsicBounds(R.drawable.icon_charging, 0, 0, 0);
            tvChargeState.setText(getString(R.string.text_charging));
            ivCharging.setVisibility(View.VISIBLE);
        } else if (level < 20) {
            //电量低
            tvChargeState.setVisibility(View.VISIBLE);
            tvChargeState.setCompoundDrawablesWithIntrinsicBounds(R.drawable.icon_low_power, 0, 0, 0);
            tvChargeState.setText(getString(R.string.text_low_pow));
            ivCharging.setVisibility(View.GONE);
        } else {
            //没在充电，电量正常
            tvChargeState.setVisibility(View.INVISIBLE);
            ivCharging.setVisibility(View.GONE);
        }
        //更新电量百分比
        tvPower.setText(level + "%");
        int resId = getResources().getIdentifier("electricity" + (int) (level / 20.0), "drawable", getPackageName());
        tvPower.setCompoundDrawablesWithIntrinsicBounds(resId, 0, 0, 0);
    }

    @Override
    public void onClick(View v) {
        if (currentGuideCount < 7) return;
        int id = v.getId();
        switch (id) {
            case R.id.btn_start_disinfection:
                presenter.onStartDisinfectionBtnClicked(this, false);
                break;
            case R.id.tv_lock:
                showLockView();
                break;
            case R.id.tv_charge:
                presenter.onGotoCharge(this);
                break;
            case R.id.tv_hostname:
                presenter.onGotoSettingPage(this);
                break;
            case R.id.ll_manual_task:
                presenter.onGotoTaskCreatePage(this, presenter.getCurrentTask());
                break;
            case R.id.rl_recent_scheduled_task:
                Object currentTask = v.getTag();
                if (currentTask == null) return;
                presenter.onGotoTaskCreatePage(this, (Task) currentTask);
                break;
            case R.id.btn_look_up_scheduled_task:
            case R.id.tv_title_recent_task:
                presenter.onGotoScheduledTaskListPage(this);
                break;
            case R.id.btn_choose_map:
            case R.id.btn_choose_wifi:
            case R.id.btn_build_map:
                onSideBarClicked(id);
                break;
        }
    }

    private void onSideBarClicked(int id) {
        dlRoot.closeDrawer(Gravity.RIGHT);

        mHandler.postDelayed(() -> {
            switch (id) {
                case R.id.btn_choose_map:
                    presenter.onChooseMap(this, Event.getIpEvent().ipAddress);
                    break;
                case R.id.btn_choose_wifi:
                    presenter.onGotoWiFiPage(this);
                    break;
                case R.id.btn_build_map:
                    presenter.onGotoMapBuildingPage(this);
                    break;
            }
        }, 300);

    }

    /**
     * 显示解锁界面
     */
    private void showLockView() {
        showScreenUnlockView(null);
    }

    /**
     * 显示解锁界面
     *
     * @param runnable
     */
    private void showScreenUnlockView(Runnable runnable) {
        screenLockWindow = new ScreenUnlockDialog(this);
        screenLockWindow.setOnScreenUnlockEventListener(this);
        screenLockWindow.show(runnable);
    }

    /**
     * 手动任务加载完成
     *
     * @param task
     */
    @Override
    public void onManualTaskLoaded(Task task) {
        if (task.taskMode == 0) {
            tvTaskMode.setText(getString(R.string.text_single_mode));
            tvTaskMode.setCompoundDrawablesWithIntrinsicBounds(R.drawable.task_single_cycle, 0, 0, 0);
            tvDurationTime.setVisibility(View.INVISIBLE);
        } else {
            tvTaskMode.setText(getString(R.string.text_duration_mode));
            tvTaskMode.setCompoundDrawablesWithIntrinsicBounds(R.drawable.task_single_cycle, 0, 0, 0);
            tvDurationTime.setVisibility(View.VISIBLE);
            tvDurationTime.setText(TimeUtils.formatTime(task.durationTime));
            tvDurationTime.setCompoundDrawablesWithIntrinsicBounds(R.drawable.task_time_cycle, 0, 0, 0);
        }

        if (task.finishAction == 0) {
            tvBackAction.setText(getString(R.string.text_go_to_charge));
            tvBackAction.setCompoundDrawablesWithIntrinsicBounds(R.drawable.task_charging, 0, 0, 0);
        } else {
            tvBackAction.setText(getString(R.string.text_go_to_starting_point_when_task_finished));
            tvBackAction.setCompoundDrawablesWithIntrinsicBounds(R.drawable.task_init_point, 0, 0, 0);
        }

        if (task.switchMode == 0) {
            tvDisinfectionMode.setText(getString(R.string.text_open_all_the_way));
            tvDisinfectionMode.setCompoundDrawablesWithIntrinsicBounds(R.drawable.task_all_clear, 0, 0, 0);
        } else if (task.switchMode == 1) {
            tvDisinfectionMode.setText(getString(R.string.text_open_in_target_point));
            tvDisinfectionMode.setCompoundDrawablesWithIntrinsicBounds(R.drawable.task_point_clear, 0, 0, 0);
        } else if (task.switchMode == 2) {
            tvDisinfectionMode.setText(getString(R.string.text_close_all_the_way));
            tvDisinfectionMode.setCompoundDrawablesWithIntrinsicBounds(R.drawable.task_all_clear, 0, 0, 0);
        }

        tvStayTime.setText(TimeUtils.formatTime(task.stayTime));
        tvStayTime.setCompoundDrawablesWithIntrinsicBounds(R.drawable.task_point_stop_time, 0, 0, 0);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onMostRecentTaskLoadEvent(RobotEvent.OnMostRecentTaskLoadEvent event) {
        onMostRecentTaskLoaded(event.task);
    }

    /**
     * 最近一条定时任务加载完成
     *
     * @param task
     */
    @Override
    public void onMostRecentTaskLoaded(Task task) {
        rlRecentTask.setTag(task);
        if (task != null) {
            ((TextView) $(R.id.tv_task_name)).setText(task.taskName);
            ((TextView) $(R.id.tv_task_time)).setText(task.startTime);
        } else {
            ((TextView) $(R.id.tv_task_name)).setText("");
            ((TextView) $(R.id.tv_task_time)).setText("");
        }
    }

    /**
     * 显示锁屏界面
     *
     * @param presenter 方法执行对象
     * @param method    密码输入成功需要调用的方法
     * @param args      方法参数
     */
    @Override
    public void showLockScreenView(MainContract.Presenter presenter, Method method, Object[] args) {
        showScreenUnlockView(() -> {
            try {
                method.invoke(presenter, args);
            } catch (IllegalAccessException | InvocationTargetException e) {
                e.printStackTrace();
            }
        });
    }

    @Override
    public void onMapListLoaded(List<MapVO> mapList) {
        String map = Event.getMapEvent().map;
        MapVO currentMap;
        int current = 0;
        for (int i = 0; i < mapList.size(); i++) {
            currentMap = mapList.get(i);
            if (currentMap.name.equals(map)) {
                currentMap.selected = true;
                current = i;
                break;
            }
        }
        EasyDialog.getInstance().dismiss();
        mapChooseDialog = new MapChooseDialog(this, mapList, current, this);
        mapChooseDialog.show();
    }

    @Override
    public void onMapListLoadedFailed(Throwable throwable) {
        EasyDialog.getInstance().dismiss();
        ToastUtils.showShortToast(getString(R.string.text_map_load_failed, throwable.getMessage()));
    }

    @Override
    public void onMapListItemSelected(MapChooseDialog mapChooseDialog, String map) {
        if (map.equals(Event.getMapEvent().map)) {
            ToastUtils.showShortToast(getString(R.string.text_do_not_apply_map_repeatedly));
            return;
        }
        mapChooseDialog.dismiss();
        EasyDialog.getLoadingInstance(this).loading(getString(R.string.text_changing_map));
        controller.applyMap(map);

    }

    @Override
    public void onNoMapSelected() {
        ToastUtils.showShortToast(getString(R.string.text_please_choose_map));
    }

    @Override
    public void onChargeTaskStart() {
        mHandler.postDelayed(chargeRunnable, 3000);
        EasyDialog.getInstance(this).onlyCancel(getString(R.string.text_going_to_charge), new EasyDialog.OnViewClickListener() {
            @Override
            public void onViewClick(Dialog dialog, int id) {
                dialog.dismiss();
                mHandler.removeCallbacks(chargeRunnable);
                if (nc.isNavigating()) {
                    controller.cancelNavigation();
                }
                if (nc.isChargingDocking()) {
                    controller.cancelCharge();
                    nc.setChargingDocking(false);
                }
            }
        });
    }

    @Override
    public void onPasswordCorrect(Dialog dialog, Runnable successRunnable) {
        dialog.dismiss();
        if (successRunnable != null) {
            successRunnable.run();
        }
    }

    @Override
    public void onPasswordError(Dialog dialog) {
        ToastUtils.showShortToast(getString(R.string.text_password_error));
    }

    @Override
    protected void onCustomTimeStamp(RobotEvent.OnTimeEvent event) {
        //如果正在计时，则不执行定时任务
        if (EasyDialog.isShow() && EasyDialog.getInstance().isTiming()) return;

        if (screenLockWindow != null && screenLockWindow.isShowing()) {
            screenLockWindow.dismiss();
        }
        if (mapChooseDialog != null && mapChooseDialog.isShowing()) {
            mapChooseDialog.dismiss();
        }
        super.onCustomTimeStamp(event);
    }
}
