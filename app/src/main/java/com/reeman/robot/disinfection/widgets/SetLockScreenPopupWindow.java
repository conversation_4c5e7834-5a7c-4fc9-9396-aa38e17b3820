package com.reeman.robot.disinfection.widgets;

import android.app.Activity;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.text.Editable;
import android.text.TextWatcher;
import android.text.method.HideReturnsTransformationMethod;
import android.text.method.PasswordTransformationMethod;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.GridLayout;
import android.widget.ImageButton;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.google.android.material.textfield.TextInputEditText;
import com.example.workomar.R;

/**
 * @ClassName: LockScreenPopupWindow.java
 * @Author: XueDong(1123988589 @ qq.com)
 * @Date: 2022/1/9 15:01
 * @Description: 设置锁屏
 */
public class SetLockScreenPopupWindow extends PopupWindow implements View.OnClickListener, TextWatcher {

    private final ImageButton ibShowPassword;
    private final ImageButton ibRollBack;
    private final TextView tvConfirm;
    private final ViewGroup root;
    private final TextInputEditText etLockScreen;
    private final boolean isOpenLock;


    public SetLockScreenPopupWindow(Activity context, boolean lockScreen) {
        this.isOpenLock = lockScreen;
        setWidth(ViewGroup.LayoutParams.MATCH_PARENT);
        setHeight(ViewGroup.LayoutParams.WRAP_CONTENT);
        setFocusable(false);
        setBackgroundDrawable(new ColorDrawable(Color.WHITE));
        setOutsideTouchable(true);
        setAnimationStyle(R.style.popupWindowTranslateAnimation);
        root = (ViewGroup) LayoutInflater.from(context).inflate(R.layout.layout_set_lock_screen_password, null);
        etLockScreen = root.findViewById(R.id.et_lock_screen);
        etLockScreen.addTextChangedListener(this);
        ibShowPassword = root.findViewById(R.id.ib_show_password);
        ibShowPassword.setOnTouchListener((v, event) -> {
            int action = event.getAction();
            if (action == KeyEvent.ACTION_DOWN) {
                etLockScreen.setTransformationMethod(HideReturnsTransformationMethod.getInstance());
            } else if (action == KeyEvent.ACTION_UP) {
                etLockScreen.setTransformationMethod(PasswordTransformationMethod.getInstance());
            }
            return true;
        });
        ibRollBack = root.findViewById(R.id.ib_roll_back);
        ibRollBack.setOnClickListener(this);
        tvConfirm = root.findViewById(R.id.tv_confirm);
        tvConfirm.setOnClickListener(this);
        GridLayout glLockScreenKeys = root.findViewById(R.id.gl_lock_screen_keys);
        for (int i = 0; i < glLockScreenKeys.getChildCount(); i++) {
            glLockScreenKeys.getChildAt(i).setOnClickListener(this);
        }
        setOnDismissListener(new OnDismissListener() {
            @Override
            public void onDismiss() {
                if (onEventListener != null) {
                    onEventListener.onDismiss();
                }
            }
        });
        setContentView(root);
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        switch (id) {
            case R.id.ib_roll_back:
                String text = etLockScreen.getText().toString();
                if (text.length() > 0) {
                    etLockScreen.setText(text.substring(0, text.length() - 1));
                }
                break;
            case R.id.tv_confirm:
                if (onEventListener != null)
                    onEventListener.onConfirm(this, etLockScreen, etLockScreen.getText().toString(), isOpenLock);
                break;
            case R.id.btn_key_1:
            case R.id.btn_key_2:
            case R.id.btn_key_3:
            case R.id.btn_key_4:
            case R.id.btn_key_5:
            case R.id.btn_key_6:
            case R.id.btn_key_7:
            case R.id.btn_key_8:
            case R.id.btn_key_9:
                String pass = etLockScreen.getText().toString();
                if (pass.length() >= 4) {
                    return;
                }
                String s = ((Button) v).getText().toString();
                etLockScreen.setText(pass + s);
                break;
        }
    }


    private OnEventListener onEventListener;

    public void setOnEventListener(OnEventListener onEventListener) {
        this.onEventListener = onEventListener;
    }

    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {

    }

    @Override
    public void afterTextChanged(Editable s) {
        if (s.length() != 0) {
            ibRollBack.setVisibility(View.VISIBLE);
        } else {
            ibRollBack.setVisibility(View.INVISIBLE);
        }
    }

    public interface OnEventListener {
        void onConfirm(SetLockScreenPopupWindow window, EditText editText, String password, boolean isOpenLock);

        void onDismiss();
    }
}
