package com.reeman.robot.disinfection.proxy;

import com.reeman.robot.disinfection.annotations.CheckPermission;
import com.reeman.robot.disinfection.constants.Constant;
import com.reeman.robot.disinfection.presenter.impl.MainPresenter;
import com.reeman.robot.disinfection.utils.SpManager;

import java.lang.annotation.Annotation;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import com.reeman.robot.disinfection.R;

public class MainPresenterHandler implements InvocationHandler {

    private final MainPresenter presenter;

    public MainPresenterHandler(MainPresenter presenter) {
        this.presenter = presenter;
    }

    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
        if (SpManager.getInstance().getBoolean(Constant.LOCK_SCREEN, Constant.DEFAULT_LOCK_SCREEN)) {
            Annotation[] annotations = method.getDeclaredAnnotations();
            for (Annotation annotation : annotations) {
                if (annotation.annotationType() == CheckPermission.class) {
                    presenter.onCheckPermission(presenter, method, args);
                    return null;
                }
            }
        }
        return method.invoke(presenter, args);
    }
}
