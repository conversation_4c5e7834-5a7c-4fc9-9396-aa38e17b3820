package com.reeman.robot.disinfection.widgets;

import android.app.Dialog;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.TextView;

import androidx.annotation.IdRes;
import androidx.annotation.LayoutRes;
import androidx.annotation.NonNull;

import com.example.workomar.R;


public class EasyDialog extends Dialog implements View.OnClickListener {
    private static EasyDialog dialog;
    private TextView tvContent;
    private Button btnCancel;
    private Button btnConfirm;
    private Button btnNeutral;
    private TextView tvTitle;
    private final Handler handler = new Handler(Looper.getMainLooper());
    private Runnable updateTask;
    private int execCount;
    private ViewGroup root;
    private boolean isTiming;

    private EasyDialog(@NonNull Context context) {
        this(context, R.layout.layout_easy_dialog);
    }

    private EasyDialog(Context context, int layoutId) {
        super(context, R.style.common_dialog_style);
        root = (ViewGroup) LayoutInflater.from(context).inflate(layoutId, null);
        setContentView(root);
        tvTitle = root.findViewById(R.id.tv_dialog_title);
        tvContent = root.findViewById(R.id.tv_content);
        btnCancel = root.findViewById(R.id.btn_cancel);
        btnConfirm = root.findViewById(R.id.btn_confirm);
        btnNeutral = root.findViewById(R.id.btn_neutral);
        btnCancel.setOnClickListener(this);
        btnConfirm.setOnClickListener(this);
        btnNeutral.setOnClickListener(this);
        setCancelable(false);
        Window window = getWindow();
        WindowManager.LayoutParams params = window.getAttributes();
        params.width = WindowManager.LayoutParams.WRAP_CONTENT;
        params.height = WindowManager.LayoutParams.WRAP_CONTENT;
        window.setAttributes(params);
    }

    private EasyDialog(Context context, int layoutId, boolean custom) {
        super(context, R.style.common_dialog_style);
        root = (ViewGroup) LayoutInflater.from(context).inflate(layoutId, null);
        btnCancel = root.findViewById(R.id.btn_cancel);
        if (btnCancel != null) {
            btnCancel.setVisibility(View.GONE);
            btnCancel.setOnClickListener(this);
        }
        setContentView(this.root);
        setCancelable(false);
        Window window = getWindow();
        WindowManager.LayoutParams params = window.getAttributes();
        params.width = WindowManager.LayoutParams.WRAP_CONTENT;
        params.height = WindowManager.LayoutParams.WRAP_CONTENT;
        window.setAttributes(params);
    }

    public static EasyDialog getInstance(Context context) {
        if (dialog == null) {
            dialog = new EasyDialog(context);
        }
        return dialog;
    }


    public static EasyDialog getInstance(Context context, @LayoutRes int layoutId) {
        if (dialog == null) {
            dialog = new EasyDialog(context, layoutId);
        }
        return dialog;
    }


    public static EasyDialog getInstance() {
        return dialog;
    }


    public static EasyDialog getLoadingInstance(Context context) {
        if (dialog == null) {
            dialog = new EasyDialog(context, R.layout.loading_dialog_view, true);
        }
        return dialog;
    }


    public static EasyDialog getCancelableLoadingInstance(Context context) {
        if (dialog == null) {
            dialog = new EasyDialog(context, R.layout.cancelable_loading_dialog_view, true);
        }
        return dialog;
    }

    public static boolean isShow() {
        return dialog != null && dialog.isShowing();
    }

    /**
     * 提示错误，自定义对话框点击事件
     *
     * @param content
     * @param onViewClickListener
     */
    public void warn(String content, OnViewClickListener onViewClickListener) {
        try {
            tvContent.setText(content);
            btnCancel.setText(R.string.text_cancel);
            btnConfirm.setText(R.string.text_confirm);
            btnConfirm.setVisibility(View.VISIBLE);
            btnNeutral.setVisibility(View.GONE);
            btnCancel.setVisibility(View.INVISIBLE);
            this.onViewClickListener = onViewClickListener;
            show();
        } catch (Exception e) {
            dismiss();
        }
    }

    /**
     * 只能取消的dialog
     *
     * @param content
     * @param listener
     */
    public void onlyCancel(String content, OnViewClickListener listener) {
        try {
            tvContent.setText(content);
            btnCancel.setText(R.string.text_cancel);
            btnConfirm.setText(R.string.text_confirm);
            btnConfirm.setVisibility(View.GONE);
            btnNeutral.setVisibility(View.GONE);
            btnCancel.setVisibility(View.VISIBLE);
            this.onViewClickListener = listener;
            show();
        } catch (Exception e) {
            dismiss();
        }
    }

    public void update(int viewId, String text) {
        try {
            TextView tvTextView = root.findViewById(viewId);
            tvTextView.setText(text);
        } catch (Exception e) {
            dismiss();
        }
    }


    public void warnWithScheduledUpdateDetail(String content, int positiveText, int negativeText, OnViewClickListener onClickListener, OnTimeStampListener listener, int rate, int total) {
        try {
            tvContent.setText(content);
            btnCancel.setText(negativeText);
            btnConfirm.setText(positiveText);
            btnConfirm.setVisibility(View.VISIBLE);
            btnNeutral.setVisibility(View.GONE);
            btnCancel.setVisibility(View.VISIBLE);
            execCount = 0;
            isTiming = true;
            updateTask = new Runnable() {
                @Override
                public void run() {
                    if (listener == null) return;
                    listener.onTimestamp(tvTitle, tvContent, btnCancel, btnNeutral, btnConfirm, execCount);
                    if (execCount < total / rate) {
                        execCount++;
                        handler.postDelayed(updateTask, rate);
                    } else {
                        isTiming = false;
                        listener.onTimeOut(dialog);
                    }
                }
            };
            handler.postDelayed(updateTask, rate);
            this.onViewClickListener = new OnViewClickListener() {
                @Override
                public void onViewClick(Dialog dialog, int id) {
                    handler.removeCallbacks(updateTask);
                    if (id == R.id.btn_cancel) isTiming = false;
                    if (onClickListener == null) return;
                    onClickListener.onViewClick(dialog, id);
                }
            };
            show();
        } catch (Exception e) {
            dismiss();
        }
    }

    /**
     * 弹出带有倒计时的对话框，每隔一定频率更新对话框的界面
     *
     * @param content
     * @param onClickListener
     * @param listener
     * @param rate
     * @param total
     */
    public void warnWithScheduledUpdate(String content, OnViewClickListener onClickListener, OnTimeStampListener listener, int rate, int total) {
        warnWithScheduledUpdateDetail(content, R.string.text_confirm, R.string.text_cancel, onClickListener, listener, rate, total);
    }

    /**
     * 提示错误，一个选项，默认点击行为是关闭dialog
     *
     * @param content
     */
    public void warnError(String content) {
        warn(content, (dialog, id) -> dialog.dismiss());
    }

    public boolean isTiming() {
        return isTiming;
    }

    /**
     * 确认对话框，自定义按钮文字
     *
     * @param positiveTxt
     * @param negativeTxt
     * @param content
     * @param onViewClickListener
     */
    public void confirm(String positiveTxt, String negativeTxt, String content, OnViewClickListener onViewClickListener) {
        try {
            tvContent.setText(content);
            btnCancel.setText(negativeTxt);
            btnConfirm.setText(positiveTxt);
            btnConfirm.setVisibility(View.VISIBLE);
            btnCancel.setVisibility(View.VISIBLE);
            btnNeutral.setVisibility(View.GONE);
            this.onViewClickListener = onViewClickListener;
            show();
        } catch (Exception e) {
            dismiss();
        }
    }

    /**
     * 两种选择对话框
     *
     * @param content
     * @param onViewClickListener
     */
    public void confirm(String content, OnViewClickListener onViewClickListener) {
        Context context = getContext();
        confirm(context.getString(R.string.text_confirm), context.getString(R.string.text_cancel), content, onViewClickListener);
    }

    /**
     * 三种选择按钮对话框
     *
     * @param positiveTxt
     * @param neutralTxt
     * @param negativeTxt
     * @param content
     * @param onViewClickListener
     */
    public void neutral(String positiveTxt, String neutralTxt, String negativeTxt, String content, OnViewClickListener onViewClickListener) {
        try {
            tvContent.setText(content);
            btnCancel.setText(negativeTxt);
            btnNeutral.setText(neutralTxt);
            btnConfirm.setText(positiveTxt);
            btnConfirm.setVisibility(View.VISIBLE);
            btnCancel.setVisibility(View.VISIBLE);
            btnNeutral.setVisibility(View.VISIBLE);
            this.onViewClickListener = onViewClickListener;
            show();
        } catch (Exception e) {
            dismiss();
        }
    }

    @Override
    public void show() {
        Window window = this.getWindow();
        window.setFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE, WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE);
        super.show();
        int uiOptions = View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                | View.SYSTEM_UI_FLAG_FULLSCREEN
                | 0x00004000;
        window.getDecorView().setSystemUiVisibility(uiOptions);
        window.clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE);
    }

    public void loading(String prompt) {
        try {
            ((TextView) root.findViewById(R.id.tv_loading_prompt)).setText(prompt);
            show();
        } catch (Exception e) {
            dismiss();
        }
    }


    public void loadingCancelable(String prompt, OnViewClickListener listener) {
        try {
            ((TextView) root.findViewById(R.id.tv_loading_prompt)).setText(prompt);
            handler.postDelayed(() -> {
                if (btnCancel != null) {
                    btnCancel.setVisibility(View.VISIBLE);
                }
            }, 25_000);
            this.onViewClickListener = listener;
            show();
        } catch (Exception e) {
            dismiss();
        }
    }

    @Override
    public void dismiss() {
        try {
            super.dismiss();
            dialog = null;
            handler.removeCallbacks(updateTask);
        } catch (Exception e) {
            dialog = null;
            handler.removeCallbacks(updateTask);
            e.printStackTrace();
        }
    }

    private OnViewClickListener onViewClickListener;

    @Override
    public void onClick(View v) {
        if (onViewClickListener != null) {
            onViewClickListener.onViewClick(dialog, v.getId());
        }
    }

    public interface OnViewClickListener {
        /**
         * 对话框被点击
         *
         * @param dialog
         * @param id
         */
        void onViewClick(Dialog dialog, @IdRes int id);
    }

    public interface OnTimeStampListener {
        /**
         * 每隔一定频率回调一次
         *
         * @param title      对话框的标题
         * @param content    对话框的内容
         * @param cancelBtn  对话框的取消按钮
         * @param neutralBtn 对话框的中间按钮
         * @param confirmBtn 对话框的确认按钮
         * @param current    当前第几次回调
         */
        void onTimestamp(TextView title, TextView content, Button cancelBtn, Button neutralBtn, Button confirmBtn, int current);

        /**
         * 超时回调
         *
         * @param dialog
         */
        void onTimeOut(EasyDialog dialog);
    }
}
