package com.reeman.robot.disinfection.activities;

import android.app.Dialog;
import android.content.Intent;
import android.os.BatteryManager;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import com.reeman.robot.disinfection.R;
import com.reeman.robot.disinfection.base.BaseActivity;
import com.reeman.robot.disinfection.constants.Constant;
import com.reeman.robot.disinfection.contract.TaskExecutingContract;
import com.reeman.robot.disinfection.event.RobotEvent;
import com.reeman.robot.disinfection.presenter.impl.TaskExecutingPresenter;
import com.reeman.robot.disinfection.repository.entities.Task;
import com.reeman.robot.disinfection.utils.SpManager;
import com.reeman.robot.disinfection.utils.TimeUtils;
import com.reeman.robot.disinfection.utils.ToastUtils;
import com.reeman.robot.disinfection.utils.VoiceHelper;
import com.reeman.robot.disinfection.widgets.EasyDialog;
import com.reeman.robot.disinfection.widgets.ScreenUnlockDialog;
import com.reeman.ros.event.Event;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.Date;

import static com.reeman.robot.disinfection.base.BaseApplication.controller;
import static com.reeman.robot.disinfection.base.BaseApplication.nc;
import static com.reeman.robot.disinfection.presenter.impl.TaskExecutingPresenter.TASK_STATE_PAUSE;
import static com.reeman.robot.disinfection.presenter.impl.TaskExecutingPresenter.TASK_STATE_RUNNING;

public class TaskExecutingActivity extends BaseActivity implements View.OnClickListener, TaskExecutingContract.View, ScreenUnlockDialog.OnScreenUnlockEventListener {

    private TextView tvPower;
    private TextView tvHostname;

    private Button btnPauseTask;

    private TextView tvTaskType;
    private TextView tvTaskMode;
    private TextView tvTaskTimeWaste;
    private TextView tvSwitchState;
    private TextView tvTaskStartTime;
    private TextView tvTaskTimeRemain;
    private TextView tvCurrentDestination;

    private TaskExecutingContract.Presenter presenter;
    private Task task;
    private Date startTime;
    private int powerLevel;
    private ScreenUnlockDialog screenLockWindow;

    @Override
    protected boolean shouldResponse2TimeEvent() {
        return true;
    }

    @Override
    protected int getLayoutRes() {
        return R.layout.activity_task_executing;
    }

    @Override
    protected void initView() {
        tvHostname = $(R.id.tv_hostname);
        tvPower = $(R.id.tv_power);
        tvTaskType = $(R.id.tv_current_task_type);
        tvTaskMode = $(R.id.tv_current_task_mode);
        tvTaskStartTime = $(R.id.tv_current_task_start_time);
        tvTaskTimeWaste = $(R.id.tv_current_task_time_waste);
        tvTaskTimeRemain = $(R.id.tv_current_task_time_remain);
        tvSwitchState = $(R.id.tv_current_disinfection_switch_state);
        tvCurrentDestination = $(R.id.tv_current_destination);

        btnPauseTask = $(R.id.btn_pause_and_resume_disinfection);
        Button btnFinishTask = $(R.id.btn_finish_task);
        btnPauseTask.setOnClickListener(this);
        btnFinishTask.setOnClickListener(this);
    }


    @Override
    protected void initData() {
        presenter = new TaskExecutingPresenter(this);
        task = getIntent().getParcelableExtra("extra");
        if (SpManager.getInstance().getInt(Constant.ROBOT_TYPE, 1) == 3) {
            task.switchMode = 1;
        }
        tvTaskType.setText(getString(R.string.text_current_task_type, task.taskType == 0 ? getString(R.string.text_manual_task) : getString(R.string.text_scheduled_task)));
        tvTaskMode.setText(getString(R.string.text_current_task_mode, task.taskMode == 0 ? getString(R.string.text_single_mode) : getString(R.string.text_duration_mode)));

        startTime = new Date();
        tvTaskStartTime.setText(getString(R.string.text_current_task_start_time, TimeUtils.format(startTime)));
        tvTaskTimeWaste.setText(getString(R.string.text_current_task_time_waste, "00:00:00"));
        if (task.taskMode == 1) {
            tvTaskTimeRemain.setVisibility(View.VISIBLE);
            tvTaskTimeRemain.setText(getString(R.string.text_current_task_time_remain, TimeUtils.format(new Date(task.durationTime))));
        } else {
            tvTaskTimeRemain.setVisibility(View.GONE);
        }
        if (task.switchMode == 0) {
            tvSwitchState.setText(getString(R.string.text_current_disinfection_switch_state, getString(R.string.text_open)));
        } else {
            tvSwitchState.setText(getString(R.string.text_current_disinfection_switch_state, getString(R.string.text_close)));
        }

        refreshPowerState(nc.getLevel());
        mHandler.postDelayed(() -> presenter.startTask(this, task, startTime, powerLevel), 300);
    }

    @Override
    protected void onResume() {
        super.onResume();
        controller.getHostName();
        nc.setOpenDetection(SpManager.getInstance().getBoolean(Constant.OPEN_DETECTION, false));
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        switch (id) {
            case R.id.btn_pause_and_resume_disinfection:
                presenter.onBtnPauseOrResumeClicked(this);
                break;
            case R.id.btn_finish_task:
                presenter.onBtnFinishTaskClicked(this);
                break;
        }
    }

    //更新任务状态
    @Override
    public void updateTaskProgress(int taskMode, long taskDuration, long waste) {
        //消耗的时长
        tvTaskTimeWaste.setText(getString(R.string.text_current_task_time_waste, TimeUtils.formatTime2(waste)));
        //剩余时长
        if (taskMode == 1) {
            tvTaskTimeRemain.setText(getString(R.string.text_current_task_time_remain, TimeUtils.formatTime2(taskDuration - waste)));
        }
    }

    //消毒开关打开
    @Override
    public void showDisinfectionSwitchTurnOnView() {
        tvSwitchState.setText(getString(R.string.text_current_disinfection_switch_state, getString(R.string.text_open)));
    }

    //消毒开关关闭
    @Override
    public void showDisinfectionSwitchTurnOffView() {
        tvSwitchState.setText(getString(R.string.text_current_disinfection_switch_state, getString(R.string.text_close)));
    }

    @Override
    public void showTaskCanceled() {
        EasyDialog.getInstance(this).warn(getString(R.string.text_task_was_canceled), new EasyDialog.OnViewClickListener() {
            @Override
            public void onViewClick(Dialog dialog, int id) {
                dialog.dismiss();
                finish();
            }
        });
    }

    @Override
    public void showTaskFinishedView() {
        if (EasyDialog.isShow()) EasyDialog.getInstance().dismiss();
        finish();
    }

    @Override
    public void showReturningJourneyView(String prompt) {
        EasyDialog.getInstance(this).onlyCancel(prompt, new EasyDialog.OnViewClickListener() {
            @Override
            public void onViewClick(Dialog dialog, int id) {
                dialog.dismiss();
                mHandler.removeCallbacks(chargeRunnable);
                controller.cancelNavigation();
                nc.setNavigating(false);
                if (nc.isChargingDocking()) {
                    controller.cancelCharge();
                    nc.setChargingDocking(false);
                }
                finish();
            }
        });
    }

    @Override
    public void showTaskStateView(int currentTaskState) {
        if (currentTaskState == TASK_STATE_RUNNING) {
            btnPauseTask.setText(getString(R.string.text_pause_task));
        } else if (currentTaskState == TASK_STATE_PAUSE) {
            btnPauseTask.setText(getString(R.string.text_resume_task));
        }
    }

    @Override
    protected void onNavigationStartResult(int code, String name) {
        if (code == 0) {
            tvCurrentDestination.setText(getString(R.string.text_current_destination, presenter.getNextDestination()));
            presenter.onPointFound();
        } else if (code == -4) {
            presenter.onPointNotFound(this);
        } else {
            presenter.onNavigationStartResult(this, code, name);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onStopTaskEvent(Event.OnStopTaskEvent event) {
        presenter.onStopTask();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onHostnameObtained(Event.OnHostnameEvent event) {
        tvHostname.setText(event.hostname);
    }

    @Override
    protected void onSensorsError(Event.OnCheckSensorsEvent event) {
        super.onSensorsError(event);
        presenter.onSensorsError(this, event);
    }

    @Override
    protected void onCustomTimeStamp(RobotEvent.OnTimeEvent event) {
        if (event.eventType == 0) {
            presenter.onLowPower(this);
        }
    }

    @Override
    protected void onNavigationCancelResult(int code) {
        presenter.onNavigationCancelResult();
    }

    @Override
    protected void onNavigationCompleteResult(int code, String name, float mileage) {
        presenter.onNavigationCompleteResult(this, code, name, mileage);
    }

    @Override
    protected void onCustomEmergencyStopStateChange(int emergencyStopState) {
        if (emergencyStopState == 0) {
            if (screenLockWindow != null && screenLockWindow.isShowing())
                screenLockWindow.dismiss();
        }
        presenter.onEmergencyStopStateChange(this, emergencyStopState);
    }

    @Override
    protected void onCustomBatteryChange() {
        refreshPowerState(nc.getLevel());
    }

    @Override
    protected void onCustomPowerConnected() {
        presenter.onStartCharge();
    }

    @Override
    protected void onCustomChargingPileNotFound() {
        presenter.onChargingPileNotFound(this);
    }

    @Override
    protected void onCustomDockFailed() {
        if (EasyDialog.isShow()) EasyDialog.getInstance().dismiss();
        VoiceHelper.play("voice_charging_dock_failed");
        EasyDialog.getInstance(this).warn(getString(R.string.voice_charging_dock_failed), new EasyDialog.OnViewClickListener() {
            @Override
            public void onViewClick(Dialog dialog, int id) {
                dialog.dismiss();
                finish();
            }
        });
    }

    @Override
    public void showCanNotReachChargingPileView(String error) {
        EasyDialog.getInstance(this).warn(error, new EasyDialog.OnViewClickListener() {
            @Override
            public void onViewClick(Dialog dialog, int id) {
                dialog.dismiss();
                finish();
            }
        });
    }

    @Override
    public void showPauseButtonVisibility(boolean gone) {
        if (nc.isOpenDetection()){
            btnPauseTask.setVisibility(gone?View.GONE:View.VISIBLE);
        }
    }

    @Override
    public void showChargingPileNotFound() {
        EasyDialog.getInstance(this).warn(getString(R.string.voice_not_found_charging_pile), new EasyDialog.OnViewClickListener() {
            @Override
            public void onViewClick(Dialog dialog, int id) {
                dialog.dismiss();
                finish();
            }
        });
    }

    //更新电量
    private void refreshPowerState(int level) {
        powerLevel = level;
        tvPower.setText(powerLevel + "%");
        int resId = getResources().getIdentifier("electricity" + (int) (powerLevel / 20.0), "drawable", getPackageName());
        tvPower.setCompoundDrawablesWithIntrinsicBounds(resId, 0, 0, 0);
    }

    //传感器异常
    @Override
    public void showSensorErrorView(String hardwareError) {
        EasyDialog.getInstance(this).warn(hardwareError, new EasyDialog.OnViewClickListener() {
            @Override
            public void onViewClick(Dialog dialog, int id) {
                dialog.dismiss();
                finish();
            }
        });
    }

    //显示锁屏界面，密码正确，恢复任务
    @Override
    public void showLockScreenView(Runnable runnable) {
        screenLockWindow = new ScreenUnlockDialog(this);
        screenLockWindow.setOnScreenUnlockEventListener(this);
        screenLockWindow.show(runnable);
    }

    //显示锁屏界面，超时继续任务，成功暂停任务
    @Override
    public void showLockScreenTimeOutView(Runnable timeoutRunnable, Runnable successRunnable) {
        screenLockWindow = new ScreenUnlockDialog(this);
        screenLockWindow.setOnScreenUnlockEventListener(this);
        screenLockWindow.showTimeOut(timeoutRunnable, successRunnable);
    }

    //密码正确
    @Override
    public void onPasswordCorrect(Dialog dialog, Runnable successRunnable) {
        dialog.dismiss();
        successRunnable.run();
    }

    //密码错误
    @Override
    public void onPasswordError(Dialog dialog) {
        ToastUtils.showShortToast(getString(R.string.text_password_error));
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onHumanDetection(Event.OnHumanDetectionEvent event) {
        presenter.onHumanDetection(this,event.result);
    }
}
