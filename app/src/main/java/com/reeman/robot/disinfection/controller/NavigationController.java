package com.reeman.robot.disinfection.controller;

import static com.reeman.robot.disinfection.base.BaseApplication.controller;

import com.reeman.robot.disinfection.R;

public class NavigationController {
    private static NavigationController INSTANCE;

    private boolean isNavigating = false;
    private boolean isChargingDocking = false;
    private int level = 0;
    private int plug = 0;
    private int emergencyStop = -1;
    private boolean isFall = false;
    private String lastSensorState = "";
    private boolean openDetection = false;
    private long openDetectionTime = 0L;

    public void reset(){
        INSTANCE = null;
        isNavigating = false;
        isChargingDocking = false;
        level=  0;
        plug = 0;
        emergencyStop = -1;
        isFall = false;
        lastSensorState = "";
    }

    public long getOpenDetectionTime() {
        return openDetectionTime;
    }

    public boolean isOpenDetection() {
        return openDetection;
    }

    public void setOpenDetection(boolean openDetection) {
        this.openDetection = openDetection;
    }

    public boolean isAcCharging() {
        return plug == 3;
    }

    public boolean isCharging() {
        return plug == 2 || plug == 3;
    }

    public String getLastSensorState() {
        return lastSensorState;
    }

    public void setLastSensorState(String lastSensorState) {
        this.lastSensorState = lastSensorState;
    }

    public boolean isFall() {
        return isFall;
    }

    public void setFall(boolean fall) {
        isFall = fall;
    }

    public int getEmergencyStop() {
        return emergencyStop;
    }

    public void setEmergencyStop(int emergencyStop) {
        this.emergencyStop = emergencyStop;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public int getPlug() {
        return plug;
    }

    public void setPlug(int plug) {
        this.plug = plug;
    }

    public boolean isNavigating() {
        return isNavigating;
    }

    public void setNavigating(boolean navigating) {
        isNavigating = navigating;
    }

    public boolean isChargingDocking() {
        return isChargingDocking;
    }

    public void setChargingDocking(boolean chargingDocking) {
        isChargingDocking = chargingDocking;
    }

    public static NavigationController getInstance() {
        if (INSTANCE == null) {
            synchronized (NavigationController.class) {
                if (INSTANCE == null) {
                    INSTANCE = new NavigationController();
                }
            }
        }
        return INSTANCE;
    }

    public NavigationController() {

    }

    public void navigationByPoint(String point) {
        isNavigating = true;
        controller.navigationByPoint(point);
    }

    public void UVControl(boolean open){
        controller.generalPowerControl(open);
    }

    public void humanDetectionControl(boolean open){
        if (openDetection){
            controller.humanDetectionControl(open);
            if (open){
                openDetectionTime = System.currentTimeMillis();
            }
        }
    }


}
