package com.reeman.robot.disinfection.widgets;

import android.content.Context;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.widget.FrameLayout;
import com.reeman.robot.disinfection.R;

public class WebViewHolder {

    private static WebView mWebView;

    public static WebView getView(Context context) {
        if (mWebView == null) {
            FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.MATCH_PARENT);
            mWebView = new WebView(context) {

                @Override
                protected boolean overScrollBy(int deltaX, int deltaY, int scrollX, int scrollY, int scrollRangeX, int scrollRangeY, int maxOverScrollX, int maxOverScrollY, boolean isTouchEvent) {
                    return false;
                }

                @Override
                public void scrollTo(int x, int y) {
                    super.scrollTo(0, 0);
                }
            };
            mWebView.setLayoutParams(params);
            WebSettings mWebSettings = mWebView.getSettings();
            mWebSettings.setSupportZoom(true);
            mWebSettings.setLoadWithOverviewMode(true);
            mWebSettings.setUseWideViewPort(true);
            mWebSettings.setDefaultTextEncodingName("UTF-8");
            mWebSettings.setLoadsImagesAutomatically(true);
            mWebSettings.setJavaScriptEnabled(true);
            mWebSettings.setDomStorageEnabled(true);
            mWebSettings.setDatabaseEnabled(true);
            mWebSettings.setCacheMode(WebSettings.LOAD_DEFAULT); // Modern cache handling
            mWebSettings.setSupportMultipleWindows(false);
            mWebSettings.setJavaScriptCanOpenWindowsAutomatically(true);
        }
        return mWebView;
    }

    public static void onResume() {
        mWebView.onResume();
    }

    public static void load(String url) {
        mWebView.loadUrl(url);
    }

    public static void onPause() {
        mWebView.onPause();
    }

    public static void destroy(){
        mWebView.destroy();
        mWebView = null;
    }
}
