package com.reeman.robot.disinfection.activities;

import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RadioGroup;
import android.widget.TextView;

import com.bigkoo.pickerview.builder.TimePickerBuilder;
import com.bigkoo.pickerview.listener.OnTimeSelectListener;
import com.bigkoo.pickerview.view.TimePickerView;
import com.reeman.robot.disinfection.R;
import com.reeman.robot.disinfection.base.BaseActivity;
import com.reeman.robot.disinfection.constants.Constant;
import com.reeman.robot.disinfection.contract.TaskCreateContract;
import com.reeman.robot.disinfection.presenter.impl.TaskCreatePresenter;
import com.reeman.robot.disinfection.repository.entities.Task;
import com.reeman.robot.disinfection.utils.ScreenUtils;
import com.reeman.robot.disinfection.utils.SoftKeyboardStateWatcher;
import com.reeman.robot.disinfection.utils.SpManager;
import com.reeman.robot.disinfection.utils.TimeUtils;
import com.reeman.robot.disinfection.utils.ToastUtils;
import com.reeman.robot.disinfection.widgets.EasyDialog;

import java.util.Calendar;
import java.util.Date;


public class TaskCreateActivity extends BaseActivity implements View.OnClickListener, TaskCreateContract.View, RadioGroup.OnCheckedChangeListener, SoftKeyboardStateWatcher.SoftKeyboardStateListener {

    private static final int TIME_TYPE_STAY = 1; //停留时间
    private static final int TIME_TYPE_DURATION = 2; //循环时长
    private static final int TIME_TYPE_START = 3; //开始时间
    private TextView tvStayTime;
    private TaskCreatePresenter manualTaskPresenter;
    private RadioGroup rgCycleMode;
    private RadioGroup rgBackMode;
    private RadioGroup rgSwitchMode;
    private LinearLayout llDurationTime;
    private TextView tvDurationTime;
    private LinearLayout llTaskRepeatTime;
    private LinearLayout llTaskStartTime;
    private TextView tvTaskStartTime;
    private LinearLayout llTaskName;
    private EditText etTaskName;
    private CheckBox[] boxes;
    private TextView tvCenterTitle;
    private TextView tvSave;
    private LinearLayout llDisinfectionModel;
    private Task task;

    @Override
    protected int getLayoutRes() {
        return R.layout.activity_task_create;
    }

    @Override
    protected boolean shouldResponse2TimeEvent() {
        return true;
    }

    @Override
    protected boolean shouldResponse2RemoteTaskEvent() {
        return true;
    }

    @Override
    protected void initView() {
        TextView tvBack = $(R.id.tv_back);
        tvBack.setOnClickListener(this);

        tvSave = $(R.id.tv_save);

        Button btnSave = $(R.id.btn_save);
        btnSave.setOnClickListener(this);

        tvCenterTitle = $(R.id.tv_title_center);

        /*单点停留时长*/
        tvStayTime = $(R.id.tv_stay_time);
        tvStayTime.setOnClickListener(this);

        /*单次执行还是时长循环*/
        rgCycleMode = $(R.id.rg_cycle_mode);
        rgCycleMode.setOnCheckedChangeListener(this);

        /*任务完成干什么*/
        rgBackMode = $(R.id.rg_back_mode);

        /*消毒开关如何打开*/
        rgSwitchMode = $(R.id.rg_switch_mode);

        /*循环时长*/
        tvDurationTime = $(R.id.tv_duration_time);
        tvDurationTime.setOnClickListener(this);
        llDurationTime = $(R.id.ll_duration_time);

        /*定时任务名称*/
        llTaskName = $(R.id.ll_task_name);
        etTaskName = $(R.id.et_task_name);

        /*任务开始时间*/
        llTaskStartTime = $(R.id.ll_task_start_time);
        tvTaskStartTime = $(R.id.tv_start_time);
        tvTaskStartTime.setOnClickListener(this);

        /*循环日期*/
        llTaskRepeatTime = $(R.id.ll_task_repeat_time);
        CheckBox cbRepeatMonday = $(R.id.cb_repeat_monday);
        CheckBox cbRepeatTuesday = $(R.id.cb_repeat_tuesday);
        CheckBox cbRepeatWednesday = $(R.id.cb_repeat_wednesday);
        CheckBox cbRepeatThursday = $(R.id.cb_repeat_thursday);
        CheckBox cbRepeatFriday = $(R.id.cb_repeat_friday);
        CheckBox cbRepeatSaturday = $(R.id.cb_repeat_saturday);
        CheckBox cbRepeatSunday = $(R.id.cb_repeat_sunday);
        boxes = new CheckBox[]{cbRepeatMonday, cbRepeatTuesday, cbRepeatWednesday, cbRepeatThursday, cbRepeatFriday, cbRepeatSaturday, cbRepeatSunday};

        /*消毒模式*/
        llDisinfectionModel = $(R.id.ll_disinfection_model);
        llDisinfectionModel.setVisibility(SpManager.getInstance().getInt(Constant.ROBOT_TYPE, 1) == 3 ? View.GONE : View.VISIBLE);
    }

    @Override
    protected void initData() {

        SoftKeyboardStateWatcher softKeyboardStateWatcher = new SoftKeyboardStateWatcher(getWindow().getDecorView());
        softKeyboardStateWatcher.addSoftKeyboardStateListener(this);

        manualTaskPresenter = new TaskCreatePresenter(this);
        Intent intent = getIntent();
        task = intent.getParcelableExtra(Constant.EXTRA);
        manualTaskPresenter.setCurrentTask(task);

        if (task.taskType == 0) {
            //手动任务 隐藏任务名称、开始时间、重复日期
            llTaskName.setVisibility(View.GONE);
            llTaskRepeatTime.setVisibility(View.GONE);
            llTaskStartTime.setVisibility(View.GONE);
            //tvSave.setVisibility(View.INVISIBLE);
            etTaskName.setHint(getString(R.string.text_manual_task));
        } else {
            //tvSave.setVisibility(View.VISIBLE);
            //定时任务 显示任务名称、开始时间、重复日期
            etTaskName.setHint(task.taskName);
            tvTaskStartTime.setText(task.startTime);
            int repeatMode = task.repeatTime;
            for (int i = 0; i < 7; i++) {
                int temp = (repeatMode >> i) & 0x01;
                boxes[i].setChecked(temp == 1);
            }
        }

        String promptTitle;
        if (task.tid == -1) {
            promptTitle = getString(R.string.text_create_disinfection_task);
            tvSave.setText(getString(R.string.text_create));
        } else {
            promptTitle = getString(R.string.text_modify_disinfection_task);
            tvSave.setText(getString(R.string.text_save));
        }
        tvCenterTitle.setText(promptTitle);

        //显示单次执行还是循环时长
        if (task.taskMode == 0) {
            rgCycleMode.check(R.id.rb_single_mode);
            llDurationTime.setVisibility(View.GONE);
        } else {
            rgCycleMode.check(R.id.rb_duration_mode);
            llDurationTime.setVisibility(View.VISIBLE);
        }
        tvStayTime.setText(TimeUtils.formatTime(task.stayTime));
        tvDurationTime.setText(TimeUtils.formatTime(task.durationTime));
        rgBackMode.check(task.finishAction == 0 ? R.id.rb_charge_when_finished : R.id.rb_back_when_finished);
        rgSwitchMode.check(task.switchMode == 0 ? R.id.rb_open_all_the_way : (task.switchMode == 1 ? R.id.rb_open_in_target_point : R.id.rb_close_all_the_way));
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.btn_save:
                onSave();
                break;
            case R.id.tv_back:
                finish();
                break;
            case R.id.tv_stay_time:
                showTimePicker(TimeUtils.str2Time(tvStayTime.getText().toString()), TIME_TYPE_STAY);
                break;
            case R.id.tv_duration_time:
                showTimePicker(TimeUtils.str2Time(tvDurationTime.getText().toString()), TIME_TYPE_DURATION);
                break;
            case R.id.tv_start_time:
                String[] time = tvTaskStartTime.getText().toString().split(":");
                int hour = Integer.parseInt(time[0]);
                int minute = Integer.parseInt(time[1]);
                showTimePicker(hour * 3600L + minute * 60L, TIME_TYPE_START);
                break;
        }
    }

    private void onSave() {
        int repeatMode = 0;
        for (int i = 0; i < boxes.length; i++) {
            int temp = boxes[i].isChecked() ? (1 << i) : 0;
            repeatMode = repeatMode | temp;
        }
        String taskName = etTaskName.getText().toString();
        manualTaskPresenter.newTask(
                this,
                TextUtils.isEmpty(taskName) ? etTaskName.getHint().toString() : taskName,
                rgCycleMode.getCheckedRadioButtonId(),
                rgBackMode.getCheckedRadioButtonId(),
                rgSwitchMode.getCheckedRadioButtonId(),
                tvStayTime.getText().toString(),
                tvDurationTime.getText().toString(),
                tvTaskStartTime.getText().toString(),
                repeatMode
        );
    }

    /**
     * 选择循环时长、停留时长、开始时间
     *
     * @param seconds 时长
     * @param type    选择什么时间
     */
    private void showTimePicker(long seconds, int type) {
        Calendar date = Calendar.getInstance();
        int hours = (int) (seconds / 3600);
        date.set(Calendar.HOUR_OF_DAY, hours);
        int minutes = (int) ((seconds - hours * 3600) / 60);
        date.set(Calendar.MINUTE, minutes);
        date.set(Calendar.SECOND, (int) (seconds - hours * 3600 - minutes * 60));
        TimePickerView timePicker = new TimePickerBuilder(this,
                new OnTimeSelectListener() {
                    @Override
                    public void onTimeSelect(Date date, View v) {
                        if (type == TIME_TYPE_STAY) {
                            tvStayTime.setText(TimeUtils.formatTime(date));
                        } else if (type == TIME_TYPE_DURATION) {
                            tvDurationTime.setText(TimeUtils.formatTime(date));
                        } else if (type == TIME_TYPE_START) {
                            String startDate = TimeUtils.formatHourAndMinute(date);
                            tvTaskStartTime.setText(startDate);
                            if ((TextUtils.isEmpty(etTaskName.getText().toString()) && task.tid == -1) || (task.tid != -1 && etTaskName.getHint().toString().endsWith(task.startTime))) {
                                etTaskName.setHint(Task.defaultScheduledTaskName(TaskCreateActivity.this, startDate));
                                task.startTime = startDate;
                            }
                        }
                    }
                })
                .setType(type == TIME_TYPE_START ? new boolean[]{false, false, false, true, true, false} : new boolean[]{false, false, false, true, true, true})
                .setLabel("", "", "", "h", "m", "s")
                .setCancelColor(getResources().getColor(R.color.color_document_gray))
                .setSubmitColor(getResources().getColor(R.color.color_document_blue))
                .setDividerColor(getResources().getColor(R.color.color_document_green_light))
                .setTitleSize(20)
                .setSubCalSize(20)
                .setDate(date)
                .setContentTextSize(32)
                .isCyclic(true)
                .isDialog(false)
                .build();
        timePicker.show();
    }


    @Override
    public void showSavingTaskView() {
        EasyDialog.getLoadingInstance(this).loading(getString(R.string.text_saving_task));
    }

    /**
     * 任务保存成功
     */
    @Override
    public void onTaskSaved() {
        Constant.lastUpdateTimeMills = System.currentTimeMillis();
        runOnUiThread(() -> {
            ToastUtils.showShortToast(getString(R.string.text_task_saved_success));
            if (EasyDialog.isShow()) {
                EasyDialog.getInstance().dismiss();
            }
            finish();
        });
    }

    /**
     * 任务保存失败
     *
     * @param message
     */
    @Override
    public void onTaskSaveFailed(String message) {
        ToastUtils.showShortToast(message);
    }

    @Override
    public void onCheckedChanged(RadioGroup group, int checkedId) {
        if (checkedId == R.id.rb_single_mode) {
            llDurationTime.setVisibility(View.GONE);
        } else {
            llDurationTime.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public void onBackPressed() {
    }

    @Override
    public void onSoftKeyboardOpened(int keyboardHeightInPx) {

    }

    @Override
    public void onSoftKeyboardClosed() {
        ScreenUtils.hideBottomUIMenu(this);
    }
}
