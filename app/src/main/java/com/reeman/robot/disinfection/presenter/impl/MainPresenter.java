package com.reeman.robot.disinfection.presenter.impl;

import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.widget.Button;
import android.widget.TextView;

import com.example.workomar.R;
import com.reeman.robot.disinfection.activities.MapBuildingActivity;
import com.reeman.robot.disinfection.activities.ScheduledTaskListActivity;
import com.reeman.robot.disinfection.activities.SettingActivity;
import com.reeman.robot.disinfection.activities.TaskCreateActivity;
import com.reeman.robot.disinfection.activities.TaskExecutingActivity;
import com.reeman.robot.disinfection.activities.WiFiConnectActivity;
import com.reeman.robot.disinfection.base.BaseActivity;
import com.reeman.robot.disinfection.constants.Constant;
import com.reeman.robot.disinfection.contract.MainContract;
import com.reeman.robot.disinfection.repository.entities.Task;
import com.reeman.robot.disinfection.request.factory.ServiceFactory;
import com.reeman.robot.disinfection.request.model.MapListResp;
import com.reeman.robot.disinfection.request.model.MapVO;
import com.reeman.robot.disinfection.request.service.RobotService;
import com.reeman.robot.disinfection.utils.SpManager;
import com.reeman.robot.disinfection.utils.VoiceHelper;
import com.reeman.robot.disinfection.widgets.EasyDialog;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.List;
import java.util.Locale;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.annotations.NonNull;
import io.reactivex.rxjava3.core.Single;
import io.reactivex.rxjava3.core.SingleObserver;
import io.reactivex.rxjava3.core.SingleSource;
import io.reactivex.rxjava3.disposables.Disposable;
import io.reactivex.rxjava3.functions.Consumer;
import io.reactivex.rxjava3.functions.Function;
import io.reactivex.rxjava3.observers.DisposableMaybeObserver;
import io.reactivex.rxjava3.schedulers.Schedulers;

import static com.reeman.robot.disinfection.base.BaseApplication.controller;
import static com.reeman.robot.disinfection.base.BaseApplication.dbRepository;
import static com.reeman.robot.disinfection.base.BaseApplication.nc;

public class MainPresenter implements MainContract.Presenter {
    private final MainContract.View view;
    private Task currentTask;

    public MainPresenter(MainContract.View view) {
        this.view = view;
    }

    @Override
    public Task getCurrentTask() {
        return currentTask;
    }

    @Override
    public void getManualTask() {

        dbRepository.getManualTask()
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(new DisposableMaybeObserver<Task>() {
                    @Override
                    public void onSuccess(@NonNull Task task) {
                        currentTask = task;
                        view.onManualTaskLoaded(currentTask);
                    }

                    @Override
                    public void onError(@NonNull Throwable e) {
                    }

                    @Override
                    public void onComplete() {
                        currentTask = Task.defaultManualTask();
                        view.onManualTaskLoaded(currentTask);
                    }
                });
    }

    @Override
    public void getMostRecentTask() {
        dbRepository.getAllScheduledTask()
                .subscribeOn(Schedulers.io())
                .flatMap(new Function<List<Task>, SingleSource<Task>>() {
                    @Override
                    public SingleSource<Task> apply(List<Task> tasks) throws Throwable {
                        if (tasks.isEmpty()) {
                            return Single.just(null);
                        }
                        Task mostRecentTask = null;
                        long distance = Long.MAX_VALUE;
                        Calendar instance = Calendar.getInstance(Locale.getDefault());
                        Calendar startTime = Calendar.getInstance(Locale.getDefault());
                        int dayOfWeek = instance.get(Calendar.DAY_OF_WEEK) - 2;
                        if (dayOfWeek < 0) dayOfWeek += 7;
                        for (Task task : tasks) {
                            //任务禁用或者今天不执行，跳过
                            if (!task.enabled || (task.repeatTime & (1 << dayOfWeek)) == 0)
                                continue;
                            String[] split = task.startTime.split(":");
                            startTime.set(Calendar.HOUR_OF_DAY, Integer.parseInt(split[0]));
                            startTime.set(Calendar.MINUTE, Integer.parseInt(split[1]));
                            //开始时间在当前时间之前，跳过
                            if (startTime.before(instance)) continue;
                            long abs = startTime.getTime().getTime() - instance.getTime().getTime();
                            if (abs > 10_000 && abs < distance) {
                                distance = abs;
                                mostRecentTask = task;
                            }
                        }
                        return Single.just(mostRecentTask);
                    }
                })
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(new SingleObserver<Task>() {
                    @Override
                    public void onSubscribe(@NonNull Disposable d) {
                    }

                    @Override
                    public void onSuccess(@NonNull Task task) {
                        view.onMostRecentTaskLoaded(task);
                    }

                    @Override
                    public void onError(@NonNull Throwable e) {
                        view.onMostRecentTaskLoaded(null);
                    }
                });
    }

    @Override
    public void onCheckPermission(MainContract.Presenter presenter, Method method, Object[] args) {
        view.showLockScreenView(presenter, method, args);
    }

    @Override
    public void onGotoCharge(Context context) {
        //急停按下
        if (nc.getEmergencyStop() == 0) {
            VoiceHelper.play("voice_emergency_stop_turn_on");
            return;
        }
        if (nc.isCharging()) {
            VoiceHelper.play("voice_already_in_charging");
            return;
        }
        VoiceHelper.play("voice_going_to_charging");
        view.onChargeTaskStart();
    }

    @Override
    public void onStartDisinfectionBtnClicked(Context context, boolean hasCheckDelay) {
        //急停按下
        if (nc.getEmergencyStop() == 0) {
            VoiceHelper.play("voice_turn_off_scram_stop_to_start_task");
            EasyDialog.getInstance(context).warnError(context.getString(R.string.voice_turn_off_scram_stop_to_start_task));
            return;
        }
        //适配器充电
        if (nc.isAcCharging()) {
            VoiceHelper.play("voice_cut_off_power_to_start_task");
            EasyDialog.getInstance(context).warnError(context.getString(R.string.voice_cut_off_power_to_start_task));
            return;
        }
        if (nc.isChargingDocking()) {
            controller.cancelCharge();
            nc.setChargingDocking(false);
        }
        if (!hasCheckDelay) {
            int delayTime = SpManager.getInstance().getInt(Constant.DELAY_TIME, Constant.DEFAULT_DELAY_TIME);
            if (delayTime != 0) {
                EasyDialog.getInstance(context).warnWithScheduledUpdateDetail(context.getString(R.string.text_going_to_start_task_in_future, delayTime), R.string.text_start_right_now, R.string.text_cancel_task, new EasyDialog.OnViewClickListener() {
                    @Override
                    public void onViewClick(Dialog dialog, int id) {
                        if (dialog != null) dialog.dismiss();
                        if (id == R.id.btn_confirm) {
                            onStartDisinfectionBtnClicked(context, true);
                        }
                    }
                }, new EasyDialog.OnTimeStampListener() {
                    @Override
                    public void onTimestamp(TextView title, TextView content, Button cancelBtn, Button neutralBtn, Button confirmBtn, int current) {
                        content.setText(context.getString(R.string.text_going_to_start_task_in_future, delayTime - current));
                    }

                    @Override
                    public void onTimeOut(EasyDialog dialog) {
                        if (dialog != null) dialog.dismiss();
                        onStartDisinfectionBtnClicked(context, true);
                    }
                }, 1000, delayTime * 1000);
                return;
            }
        }
        if (nc.isChargingDocking()) {
            controller.cancelCharge();
            nc.setChargingDocking(false);
        }
        VoiceHelper.play("voice_going_to_start_disinfection_task");
        Intent intent = new Intent(context, TaskExecutingActivity.class);
        intent.putExtra("extra", getCurrentTask());
        context.startActivity(intent);
    }

    @Override
    public void onChooseMap(Context context, String ipAddress) {
        EasyDialog.getLoadingInstance(context).loading(context.getString(R.string.text_loading_map_list));
        RobotService robotService = ServiceFactory.getRobotService();
        robotService.getMapList("http://" + ipAddress + "/reeman/map_list")
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<List<MapListResp>>() {
                    @Override
                    public void accept(List<MapListResp> mapListResponse) throws Throwable {
                        if (mapListResponse == null || mapListResponse.size() == 0) {
                            view.onMapListLoaded(Collections.emptyList());
                            return;
                        }
                        List<MapVO> mapVOList = new ArrayList<>();
                        for (int i = 0; i < mapListResponse.size(); i++) {
                            mapVOList.add(new MapVO(mapListResponse.get(i).name, mapListResponse.get(i).alias, false));
                        }
                        view.onMapListLoaded(mapVOList);
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Throwable {
                        view.onMapListLoadedFailed(throwable);
                    }
                });
    }


    @Override
    public void onGotoSettingPage(Context context) {
        BaseActivity.start(context, SettingActivity.class);
    }

    @Override
    public void onGotoTaskCreatePage(Context context, Task currentTask) {
        BaseActivity.start(context, TaskCreateActivity.class, currentTask);
    }

    @Override
    public void onGotoScheduledTaskListPage(Context context) {
        BaseActivity.start(context, ScheduledTaskListActivity.class, null);
    }

    @Override
    public void onGotoWiFiPage(Context context) {
        BaseActivity.start(context, WiFiConnectActivity.class, null);
    }

    @Override
    public void onGotoMapBuildingPage(Context context) {
        BaseActivity.start(context, MapBuildingActivity.class, null);
    }
}
