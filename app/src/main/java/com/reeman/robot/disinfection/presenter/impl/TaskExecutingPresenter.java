package com.reeman.robot.disinfection.presenter.impl;

import android.app.Dialog;
import android.content.Context;
import android.os.CountDownTimer;
import android.text.TextUtils;
import android.util.Log;

import com.elvishew.xlog.XLog;
import com.google.gson.Gson;
import com.example.workomar.R;
import com.reeman.robot.disinfection.activities.MainActivity;
import com.reeman.robot.disinfection.constants.Constant;
import com.reeman.robot.disinfection.constants.Errors;
import com.reeman.robot.disinfection.contract.TaskExecutingContract;
import com.reeman.robot.disinfection.event.RobotEvent;
import com.reeman.robot.disinfection.repository.entities.Task;
import com.reeman.robot.disinfection.request.factory.ServiceFactory;
import com.reeman.robot.disinfection.request.model.TaskRecord;
import com.reeman.robot.disinfection.utils.LocaleUtil;
import com.reeman.robot.disinfection.utils.SpManager;
import com.reeman.robot.disinfection.utils.VoiceHelper;
import com.reeman.robot.disinfection.widgets.EasyDialog;
import com.reeman.robot.disinfection.widgets.SimpleCountDownTimer;
import com.reeman.ros.event.Event;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.util.Date;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import io.reactivex.rxjava3.functions.Consumer;
import io.reactivex.rxjava3.schedulers.Schedulers;

import static com.reeman.robot.disinfection.base.BaseApplication.controller;
import static com.reeman.robot.disinfection.base.BaseApplication.nc;

public class TaskExecutingPresenter implements TaskExecutingContract.Presenter {

    private final TaskExecutingContract.View view;

    //当前消毒点
    private int currentDisinfectionPoint = 0;

    //任务计时定时器
    private CountDownTimer durationCountDownTimer;

    //停留时间计时器
    private CountDownTimer stayCountDownTimer;

    //红外摄像头检测到人计时器
    private CountDownTimer humanDetectionCountDownTimer;

    //记录方向
    private boolean isForward = true;

    //找不到目标点计数
    private int canNotFindPointCount = 0;

    //第一个找到的目标点
    private int firstFoundPoint = -1;

    //当前任务
    private Task task;

    //当前任务所处阶段
    private int currentTaskState = TASK_STATE_RUNNING;

    public static final int TASK_STATE_RUNNING = 0; //运行中
    public static final int TASK_STATE_PAUSE = 1;   //暂停中
    public static final int TASK_STATE_RETURNING = 2; //返程中

    //剩余停留时长
    private long stayTimeMillsUntilFinished = 0;

    //剩余时长循环
    private long durationTimeMillsUtilFinished = 0;

    //开始时间
    private Date startTime;

    //开始电量
    private int startPower;

    //失败点位
    private final Set<String> failedPoints = new HashSet<>();
    private long lastClickTimeMills;

    private boolean unmanned = true;


    public TaskExecutingPresenter(TaskExecutingContract.View view) {
        this.view = view;
    }

    @Override
    public String getNextDestination() {
        return currentDisinfectionPoint + "";
    }

    public int getCurrentDisinfectionPoint() {
        return currentDisinfectionPoint;
    }


    private void gotoTargetPoint(String point) {
        view.showPauseButtonVisibility(false);
        nc.navigationByPoint(point);
    }

    public String calcNextTargetPoint() {
        if (isForward) {
            ++currentDisinfectionPoint;
        } else {
            --currentDisinfectionPoint;
        }
        if (currentDisinfectionPoint < 0) {
            currentDisinfectionPoint = firstFoundPoint != -1 ? firstFoundPoint : 1;
            isForward = !isForward;
        }
        return currentDisinfectionPoint + "";
    }


    @Override
    public void startTask(Context context, Task task, Date startTime, int startPower) {
        this.task = task;
        this.startTime = startTime;
        this.startPower = startPower;

        XLog.w("开始任务：" + task.toString());

        //开始任务计时
        long taskDuration = task.taskMode == 0 ? Integer.MAX_VALUE : task.durationTime * 1000;
        startDurationCountdownTimer(context, taskDuration);

        //全程打开消毒则任务开始时打开消毒开关
        if (task.switchMode == 0) {
            nc.UVControl(true);
            view.showDisinfectionSwitchTurnOnView();
        }

        gotoTargetPoint(calcNextTargetPoint());
    }

    private void startDurationCountdownTimer(Context context, long taskDuration) {
        durationCountDownTimer = new SimpleCountDownTimer(taskDuration, 1000) {
            private int timeTick = 0;

            @Override
            public void onFinish() {
                onGlobalTimerFinished(context);
            }

            @Override
            public void onTick(long millisUntilFinished) {
                durationTimeMillsUtilFinished = millisUntilFinished;
                view.updateTaskProgress(task.taskMode, task.durationTime, task.taskMode == 1 ? (task.durationTime * 1000 - millisUntilFinished) / 1000 : (Integer.MAX_VALUE - millisUntilFinished) / 1000);
                //返程中，不播报
                if (currentTaskState == TASK_STATE_RETURNING) return;
                //播放中，不播报
                if (VoiceHelper.isPlaying()) return;

                if (++timeTick >= 8) {
                    timeTick = 0;
                    //关闭语音播报，不播报
                    if (!SpManager.getInstance().getBoolean(Constant.VOICE_BROADCAST, Constant.DEFAULT_VOICE_BROADCAST))
                        return;
                    //播放指定音频文件
                    int localeType = SpManager.getInstance().getInt(Constant.KEY_LANGUAGE_TYPE, Constant.DEFAULT_LANGUAGE_TYPE);
                    File file = new File(context.getFilesDir() + "/disinfection_voice/voice_disinfection_prompt_" + LocaleUtil.getLanguage(localeType) + ".wav");
                    if (file.exists()) {
                        VoiceHelper.playFile(file.getAbsolutePath());
                        return;
                    }
                    //播放默认音频文件
                    VoiceHelper.play("voice_stay_away_from_me");
                }
            }
        };
        durationCountDownTimer.start();
    }

    @Override
    public void onBtnFinishTaskClicked(Context context) {
        boolean lockScreen = SpManager.getInstance().getBoolean(Constant.LOCK_SCREEN, Constant.DEFAULT_LOCK_SCREEN);
        if (lockScreen) {
            onPauseTask();
            view.showLockScreenTimeOutView(() -> {
                XLog.w("密码输入超时，结束失败，继续任务");
                onResumeTask(context);
            }, () -> onCancelTask(true));
            return;
        }
        onCancelTask(true);
    }

    private void onCancelTask(boolean isClick) {
        XLog.w(isClick ? "点击结束按钮，取消任务" : "远程结束任务");
        VoiceHelper.play("voice_task_finished");
        //上传消毒日志
        uploadTaskRecord(TaskRecord.create(task.taskName, task.taskMode, startTime.getTime(), 1, startPower, failedPoints.toString(), ""));
        if (nc.isChargingDocking()) controller.cancelCharge();
        if (durationCountDownTimer != null) durationCountDownTimer.cancel();
        if (stayCountDownTimer != null) stayCountDownTimer.cancel();
        cancelHumanDetectionCountDownTimer();
        controller.cancelNavigation();
        nc.humanDetectionControl(false);
        nc.UVControl(false);
        view.showTaskFinishedView();
    }

    @Override
    public void onBtnPauseOrResumeClicked(Context context) {
        if (System.currentTimeMillis() - lastClickTimeMills < 300) return;
        lastClickTimeMills = System.currentTimeMillis();
        boolean lockScreen = SpManager.getInstance().getBoolean(Constant.LOCK_SCREEN, Constant.DEFAULT_LOCK_SCREEN);
        if (humanDetectionCountDownTimer != null) {
            humanDetectionCountDownTimer.cancel();
            humanDetectionCountDownTimer = null;
        }
        if (currentTaskState == TASK_STATE_RUNNING) {
            onPauseTask();
            XLog.w("暂停任务");
            if (lockScreen) {
                view.showLockScreenTimeOutView(() -> {
                    XLog.w("输入密码超时，继续任务");
                    onResumeTask(context);
                }, () -> {
                });
            }
        } else if (currentTaskState == TASK_STATE_PAUSE) {
            if (lockScreen) {
                view.showLockScreenView(() -> onResumeTask(context));
                return;
            }
            onResumeTask(context);
        }
    }

    /**
     * 当恢复任务
     */
    private void onResumeTask(Context context) {
        XLog.w("恢复任务");
        currentTaskState = TASK_STATE_RUNNING;
        view.showTaskStateView(currentTaskState);
        //剩余时间较短
        if (durationTimeMillsUtilFinished < 500) {
            onTaskFinished(context);
            return;
        }

        //剩余时间较长，启动任务计时器
        startDurationCountdownTimer(context, durationTimeMillsUtilFinished);

        //剩余停留时间较长，则重启停留定时器
        if (stayTimeMillsUntilFinished >= 500) {

            if (task.switchMode == 1 || task.switchMode == 0) {
                nc.UVControl(true);
                view.showDisinfectionSwitchTurnOnView();
            }

            startStayCountDownTimer(stayTimeMillsUntilFinished);
            return;
        }

        //中途被暂停，如果全程打开消毒则打开消毒，继续前往下一个目标点
        if (task.switchMode == 0) {
            nc.UVControl(true);
            view.showDisinfectionSwitchTurnOnView();
        }
        gotoTargetPoint(calcNextTargetPoint());
    }

    /**
     * 当暂停任务
     */
    private void onPauseTask() {
        currentTaskState = TASK_STATE_PAUSE;
        if (nc.isNavigating()) controller.cancelNavigation();
        nc.UVControl(false);
        view.showDisinfectionSwitchTurnOffView();
        if (stayTimeMillsUntilFinished == 0) {
            currentDisinfectionPoint--;
        }
        if (durationCountDownTimer != null) durationCountDownTimer.cancel();
        if (stayCountDownTimer != null) stayCountDownTimer.cancel();
        view.showTaskStateView(currentTaskState);
    }

    /**
     * 任务定时器结束（定时任务结束)
     */
    private void onGlobalTimerFinished(Context context) {
        XLog.w("计时结束，正常结束任务");
        if (durationCountDownTimer != null) {
            durationCountDownTimer.cancel();
            durationCountDownTimer = null;
        }
        durationTimeMillsUtilFinished = 0;
        if (nc.isNavigating()) controller.cancelNavigation();
        if (stayCountDownTimer != null) {
            stayCountDownTimer.cancel();
            stayCountDownTimer = null;
        }
        view.showPauseButtonVisibility(false);
        cancelHumanDetectionCountDownTimer();
        onTaskFinished(context);
    }

    private void onTaskFinished(Context context) {
        currentTaskState = TASK_STATE_RETURNING;
        cancelHumanDetectionCountDownTimer();
        uploadTaskRecord(TaskRecord.create(task.taskName, task.taskMode, startTime.getTime(), 0, startPower, failedPoints.toString(), ""));

        if (task.switchMode == 0 || task.switchMode == 1) {
            nc.humanDetectionControl(false);
            nc.UVControl(false);
            view.showDisinfectionSwitchTurnOffView();
        }

        if (task.finishAction == 0) {
            VoiceHelper.play("voice_task_finish_and_go_to_charge");
            nc.navigationByPoint(Constant.chargePoint.name);
            view.showReturningJourneyView(context.getString(R.string.text_going_to_charge));
        } else {
            VoiceHelper.play("voice_task_finish_and_go_to_start_point");
            gotoTargetPoint(firstFoundPoint + "");
            view.showReturningJourneyView(context.getString(R.string.text_going_to_start_point));
        }
    }

    private void uploadTaskRecord(TaskRecord taskRecord) {
        XLog.w("上传任务记录：" + taskRecord.toString());
        ServiceFactory
                .getRobotService()
                .syncTaskRecord(Event.getOnHostnameEvent().hostname, taskRecord)
                .subscribeOn(Schedulers.io())
                .observeOn(Schedulers.io())
                .subscribe(new Consumer<Map<String, String>>() {
                    @Override
                    public void accept(Map<String, String> map) throws Throwable {
                        Log.w("xuedong", new Gson().toJson(map));
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Throwable {

                    }
                });
    }

    private void startStayCountDownTimer(long timeMills) {
        stayCountDownTimer = new SimpleCountDownTimer(timeMills, 1000) {

            @Override
            public void onTick(long millisUntilFinished) {
                stayTimeMillsUntilFinished = millisUntilFinished;
            }

            @Override
            public void onFinish() {
                onStayFinished();
            }
        };
        stayCountDownTimer.start();
    }

    /**
     * 开始充电
     */
    @Override
    public void onStartCharge() {
        //VoiceHelper.play("voice_start_charging");
        view.showTaskFinishedView();
    }

    @Override
    public void onSensorsError(Context context, Event.OnCheckSensorsEvent event) {
        //传感器异常 任务结束
        uploadTaskRecord(TaskRecord.create(task.taskName, task.taskMode, startTime.getTime(), 3, startPower, failedPoints.toString(), event.toString()));
        if (task.switchMode == 0 && currentTaskState != TASK_STATE_RETURNING) {
            //全程消毒，且不是前往充电桩或起始点，则下发停止消毒命令
            nc.UVControl(false);
        }
        if (durationCountDownTimer != null) durationCountDownTimer.cancel();
        view.showSensorErrorView(Errors.getSensorError(context, event));
    }

    @Override
    public void onHumanDetection(Context context, int code) {
        if (code != 0) {
            if (System.currentTimeMillis() - nc.getOpenDetectionTime()<800){
                return;
            }
            if (unmanned && currentTaskState == TASK_STATE_RUNNING) {
                unmanned = false;
                onPauseTask();
            }
            XLog.w("有人,刷新时间");
            startHumanDetectionCountDownTimer(context);
        }
    }

    @Override
    public void onEmergencyStopStateChange(Context context, int emergencyStopState) {
        if (emergencyStopState == 0) {
            XLog.w("急停开关按下，任务结束");
            if (EasyDialog.isShow()) EasyDialog.getInstance().dismiss();
            if (durationCountDownTimer != null) {
                durationCountDownTimer.cancel();
                durationCountDownTimer = null;
            }
            if (stayCountDownTimer != null) {
                stayCountDownTimer.cancel();
                stayCountDownTimer = null;
            }
            cancelHumanDetectionCountDownTimer();
            nc.humanDetectionControl(false);
            nc.UVControl(false);
            view.showTaskCanceled();
        }
    }

    @Override
    public void onPointFound() {
        canNotFindPointCount = 0;
        if (firstFoundPoint == -1) {
            firstFoundPoint = Integer.parseInt(getNextDestination());
        }
    }

    /**
     * 目标点位找不到
     *
     * @param context
     */
    @Override
    public void onPointNotFound(Context context) {
        XLog.w("未找到目标点");
        //之前找到过消毒起始点，现在找不到了（任务执行过程中，动态删除点位）
        if (firstFoundPoint != -1 && firstFoundPoint == getCurrentDisinfectionPoint()) {
            onStartPointNotFound(context);
            return;
        }

        //五次找不到目标点
        if (++canNotFindPointCount >= 5) {

            //重置计数
            canNotFindPointCount = 0;

            //起始点都没找到
            if (-1 == firstFoundPoint) {
                onStartPointNotFound(context);
                return;
            }

            //翻转方向
            reverseDirection();

            //单次执行则认为任务结束
            if (task.taskMode == 0) {
                if (durationCountDownTimer != null) {
                    durationCountDownTimer.cancel();
                    durationCountDownTimer = null;
                }
                XLog.w("单次循环，任务正常结束");
                onTaskFinished(context);
                return;
            }

            //时长循环则翻转方向后继续尝试
            gotoTargetPoint(calcNextTargetPoint());
            return;
        }

        //小于五次找不到继续尝试
        gotoTargetPoint(calcNextTargetPoint());
    }

    @Override
    public void onStopTask() {
        onCancelTask(false);
    }

    @Override
    public void onNavigationStartResult(Context context, int code, String name) {
        String startNaviErrorByCode = Errors.getNavigationStartError(context, code);
        nc.setNavigating(false);
        if (!TextUtils.isEmpty(startNaviErrorByCode)) {
            //传感器异常 任务结束
            uploadTaskRecord(TaskRecord.create(task.taskName, task.taskMode, startTime.getTime(), 3, startPower, failedPoints.toString(), startNaviErrorByCode));
            if (task.switchMode == 0 && currentTaskState != TASK_STATE_RETURNING) {
                //全程消毒，且不是前往充电桩或起始点，则下发停止消毒命令
                nc.UVControl(false);
            }
            if (durationCountDownTimer != null) durationCountDownTimer.cancel();
            EasyDialog.getInstance(context).warn(startNaviErrorByCode, (dialog, id) -> {
                dialog.dismiss();
                view.showTaskFinishedView();
            });
        }
    }

    @Override
    public void onNavigationCompleteResult(Context context, int code, String name, float mileage) {
        if (code == 0) {
            //到达充电桩
            if (Constant.chargePoint.name.equals(name)) {
                nc.setChargingDocking(true);
                VoiceHelper.play("voice_start_docking_charging_pile");
                return;
            }

            //到达起始点
            if (currentTaskState == TASK_STATE_RETURNING) {
                view.showTaskFinishedView();
                return;
            }

            //停留时间为零，直接前往下一个目标点
            if (task.stayTime == 0) {
                gotoTargetPoint(calcNextTargetPoint());
                return;
            }

            //停留时间不为零，如果是目标点消毒，打开消毒开关
            if (task.switchMode == 1) {
                nc.humanDetectionControl(true);
                nc.UVControl(true);
                view.showPauseButtonVisibility(true);
                view.showDisinfectionSwitchTurnOnView();
            }

            //开始停留计时
            startStayCountDownTimer(task.stayTime * 1000);
            return;
        }
        failedPoints.add(name);
        if (context.getString(R.string.text_charging_pile).equals(name)) {
            uploadTaskRecord(TaskRecord.create(task.taskName, task.taskMode, startTime.getTime(), 2, startPower, failedPoints.toString(), ""));
            view.showCanNotReachChargingPileView(context.getString(R.string.texT_navigation_failure));
            return;
        }
        gotoTargetPoint(calcNextTargetPoint());
    }

    @Override
    public void onNavigationCancelResult() {
        if (currentTaskState != TASK_STATE_PAUSE && currentTaskState != TASK_STATE_RETURNING)
            view.showTaskCanceled();
    }

    @Override
    public void onLowPower(Context context) {
        XLog.w("任务过程中触发低电去充电");
        if (durationCountDownTimer != null) {
            durationCountDownTimer.cancel();
            durationCountDownTimer = null;
        }
        if (stayCountDownTimer != null) {
            stayCountDownTimer.cancel();
            stayCountDownTimer = null;
        }
        cancelHumanDetectionCountDownTimer();
        nc.humanDetectionControl(false);
        nc.UVControl(false);
        view.showPauseButtonVisibility(false);
        currentTaskState = TASK_STATE_RETURNING;
        if (nc.isNavigating()) {
            controller.cancelNavigation();
        }
        if (!nc.isChargingDocking()) {
            VoiceHelper.play("voice_low_power_go_to_charge");
            nc.navigationByPoint(Constant.chargePoint.name);
            view.showReturningJourneyView(context.getString(R.string.text_going_to_charge));
        }
    }

    @Override
    public void onChargingPileNotFound(Context context) {
        XLog.w("未找到充电桩");
        VoiceHelper.play("voice_not_found_charging_pile");
        view.showChargingPileNotFound();
    }

    private void onStartPointNotFound(Context context) {
        XLog.w("未找到消毒起始点，异常结束任务");
        if (task.switchMode == 0) {
            nc.UVControl(false);
            view.showDisinfectionSwitchTurnOffView();
        }

        if (durationCountDownTimer != null) {
            durationCountDownTimer.cancel();
            durationCountDownTimer = null;
        }

        EasyDialog.getInstance(context).warn(context.getString(R.string.text_can_not_find_starting_point), new EasyDialog.OnViewClickListener() {
            @Override
            public void onViewClick(Dialog dialog, int id) {
                dialog.dismiss();
                view.showTaskFinishedView();
            }
        });
    }

    private void reverseDirection() {
        if (isForward) {
            currentDisinfectionPoint -= 5;
        } else {
            currentDisinfectionPoint += 5;
        }
        isForward = !isForward;
    }

    /**
     * 停留完成
     */
    private void onStayFinished() {
        stayCountDownTimer = null;
        stayTimeMillsUntilFinished = 0;
        if (task.switchMode == 1) {
            nc.humanDetectionControl(false);
            nc.UVControl(false);
            view.showDisinfectionSwitchTurnOffView();
        }
        gotoTargetPoint(calcNextTargetPoint());
    }

    private void startHumanDetectionCountDownTimer(Context context) {
        if (humanDetectionCountDownTimer != null) {
            humanDetectionCountDownTimer.cancel();
            humanDetectionCountDownTimer = null;
        }
        humanDetectionCountDownTimer = new SimpleCountDownTimer(15_000, 1000) {
            @Override
            public void onFinish() {
                XLog.w("无人,恢复任务");
                unmanned = true;
                onResumeTask(context);
            }
        };
        humanDetectionCountDownTimer.start();
    }

    private void cancelHumanDetectionCountDownTimer() {
        if (humanDetectionCountDownTimer != null) {
            humanDetectionCountDownTimer.cancel();
            humanDetectionCountDownTimer = null;
        }
        unmanned = true;
        nc.humanDetectionControl(false);
    }


}
