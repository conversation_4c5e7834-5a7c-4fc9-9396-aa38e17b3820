package com.reeman.robot.disinfection.repository.db;

import android.content.Context;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.room.Database;
import androidx.room.Room;
import androidx.room.RoomDatabase;
import androidx.room.TypeConverters;
import androidx.sqlite.db.SupportSQLiteDatabase;

import com.reeman.robot.disinfection.BuildConfig;
import com.reeman.robot.disinfection.repository.converter.DateConverter;
import com.reeman.robot.disinfection.repository.dao.TaskDao;
import com.reeman.robot.disinfection.repository.entities.Task;
import com.reeman.robot.disinfection.R;

@Database(entities = {Task.class}, exportSchema = false, version = 1)
@TypeConverters(DateConverter.class)
public abstract class AppDataBase extends RoomDatabase {

    private static final String DB_NAME = "db_task";

    private static AppDataBase sInstance;

    public abstract TaskDao taskDao();

    public static AppDataBase getInstance(Context context) {
        if (sInstance == null) {
            synchronized (AppDataBase.class) {
                if (sInstance == null) {
                    sInstance = Room.databaseBuilder(context, AppDataBase.class, DB_NAME)
                            .addCallback(new Callback() {
                                @Override
                                public void onCreate(@NonNull SupportSQLiteDatabase db) {
                                    Log.w(BuildConfig.TAG, "AppDataBase onCreate");
                                }

                                @Override
                                public void onOpen(@NonNull SupportSQLiteDatabase db) {
                                    Log.w(BuildConfig.TAG, "AppDataBase onOpen");
                                }

                                @Override
                                public void onDestructiveMigration(SupportSQLiteDatabase db) {
                                    Log.w(BuildConfig.TAG, "AppDataBase onDestructiveMigration");
                                }
                            })
                            .build();
                }
            }
        }
        return sInstance;
    }


}
