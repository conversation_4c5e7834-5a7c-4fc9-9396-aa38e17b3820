package com.reeman.robot.disinfection.activities;

import static com.reeman.robot.disinfection.base.BaseApplication.controller;

import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.ListView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.reeman.robot.disinfection.R;
import com.reeman.robot.disinfection.SplashActivity;
import com.reeman.robot.disinfection.base.BaseActivity;
import com.reeman.robot.disinfection.constants.BuglyConstants;
import com.reeman.robot.disinfection.constants.Constant;
import com.reeman.robot.disinfection.utils.LocaleUtil;
import com.reeman.robot.disinfection.utils.SpManager;
import com.reeman.robot.disinfection.widgets.EasyDialog;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class LanguageChooseActivity extends BaseActivity implements View.OnClickListener, AdapterView.OnItemClickListener {

    private Button btnConfirm;
    private ImageButton ibBack;
    private ListView lvLanguageList;
    private ArrayAdapter<String> adapter;
    private int currentLanguageType;
    private int originLanguageType;
    private String from;

    @Override
    protected boolean shouldResponse2TimeEvent() {
        return from == null;
    }

    @Override
    protected boolean disableBottomNavigationBar() {
        return false;
    }

    public static void startActivity(Context context, String from) {
        Intent intent = new Intent(context, LanguageChooseActivity.class);
        intent.putExtra(Constant.EXTRA, from);
        context.startActivity(intent);
    }

    @Override
    protected void initView() {
        ibBack = $(R.id.tv_back);
        btnConfirm = $(R.id.btn_confirm);
        lvLanguageList = $(R.id.lv_language_list);
        lvLanguageList.setOnItemClickListener(this);
        ibBack.setOnClickListener(this);
        btnConfirm.setOnClickListener(this);
    }

    @Override
    protected int getLayoutRes() {
        return R.layout.activity_language_choose;
    }

    @Override
    protected void initData() {
        from = getIntent().getStringExtra(Constant.EXTRA);

        refreshPromptBtn();

        currentLanguageType = SpManager.getInstance().getInt(Constant.KEY_LANGUAGE_TYPE, Constant.DEFAULT_LANGUAGE_TYPE);

        if (currentLanguageType == -1) {
            currentLanguageType = LocaleUtil.getLocaleType();
        }
        originLanguageType = currentLanguageType;

        List<String> languages = new ArrayList<>();
        Collections.addAll(languages, getResources().getStringArray(R.array.languages));
        adapter = new ArrayAdapter<String>(this, R.layout.layout_language_item, R.id.tv_language_name, languages) {

            @Override
            public View getView(int position, @Nullable View convertView, @NonNull ViewGroup parent) {
                View root = super.getView(position, convertView, parent);
                ImageView ivLanguageState = root.findViewById(R.id.iv_language_state);
                if (position == currentLanguageType) {
                    ivLanguageState.setImageResource(R.drawable.icon_language_checked);
                } else {
                    ivLanguageState.setImageResource(R.drawable.icon_language_normal);
                }
                return root;
            }
        };
        lvLanguageList.setAdapter(adapter);
    }

    private void refreshPromptBtn() {
        if (SplashActivity.class.getSimpleName().equals(from)) {
            btnConfirm.setText(getString(R.string.text_next_step));
            ibBack.setVisibility(View.INVISIBLE);
        } else {
            btnConfirm.setText(getString(R.string.text_confirm));
            ibBack.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.tv_back:
                //不保存，则还原原来的语言
                if (currentLanguageType != originLanguageType)
                    LocaleUtil.changeAppLanguage(getResources(), originLanguageType);
                finish();
                break;
            case R.id.btn_confirm:
                //保存
                onLanguageSelected();
                break;
        }
    }

    private void onLanguageSelected() {
        SpManager.getInstance().edit().putInt(Constant.KEY_LANGUAGE_TYPE, currentLanguageType).apply();
        if (from != null) {
            //没引导完成，则跳转引导界面
            BuglyConstants.updateBuglyStrings(getResources());
            SpManager.getInstance().edit().putBoolean(Constant.IS_LANGUAGE_CHOSEN, true).apply();
            BaseActivity.start(this, GuideActivity.class, null);
        } else if (originLanguageType != currentLanguageType) {
            //从设置界面进入，修改了语言，则重启应用
            restartApp();
        } else {
            //从设置界面进入，没有修改语言
            finish();
        }
    }

    private void restartApp() {
        SpManager.getInstance().edit().remove(Constant.DISINFECTION_PROMPT).apply();
        File file = new File(getFilesDir() + "/disinfection_voice");
        if (file.exists()) {
            File[] files = file.listFiles();
            if (files != null && files.length > 0) {
                for (File temp : files) {
                    temp.delete();
                }
            }
        }
        EasyDialog.getInstance(this).warn(getString(R.string.text_restart_for_configuration_change), new EasyDialog.OnViewClickListener() {
            @Override
            public void onViewClick(Dialog dialog, int id) {
                if (id == R.id.btn_confirm) {
                    dialog.dismiss();
                    controller.stopListen();
                    Intent intent = new Intent(LanguageChooseActivity.this, SplashActivity.class);
                    intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_NEW_TASK);
                    startActivity(intent);
                }
            }
        });
    }

    @Override
    public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
        currentLanguageType = position;
        LocaleUtil.changeAppLanguage(getResources(), currentLanguageType);
        refreshPromptBtn();
        adapter.notifyDataSetChanged();
    }
}
