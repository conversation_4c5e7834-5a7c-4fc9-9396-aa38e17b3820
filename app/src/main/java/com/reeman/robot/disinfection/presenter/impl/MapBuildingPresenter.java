/**
 * Copyright (C), 2015-2022, XXX有限公司
 * FileName: MapBuildingPresenter
 * Author: 11239
 * Date: 2022/1/9 17:18
 * Description:
 * History:
 * <author> <time> <version> <desc>
 * 作者姓名 修改时间 版本号 描述
 */
package com.reeman.robot.disinfection.presenter.impl;


import android.app.Dialog;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;

import com.example.workomar.R;
import com.reeman.robot.disinfection.constants.Constant;
import com.reeman.robot.disinfection.constants.Errors;
import com.reeman.robot.disinfection.constants.State;
import com.reeman.robot.disinfection.contract.MapBuildingContract;
import com.reeman.robot.disinfection.utils.VoiceHelper;
import com.reeman.robot.disinfection.widgets.EasyDialog;

import java.util.ArrayList;
import java.util.List;

import static com.reeman.robot.disinfection.base.BaseApplication.controller;
import static com.reeman.robot.disinfection.base.BaseApplication.nc;

public class MapBuildingPresenter implements MapBuildingContract.Presenter {

    private final List<String> list = new ArrayList<>();
    private final MapBuildingContract.View view;
    private final Handler mHandler = new Handler(Looper.getMainLooper());
    private int pointType;
    private int pointCount = 1;
    private int currentMode = 1;

    private int currentTestPoint = 0;

    //是否是在测试消毒点
    private boolean isPointTest = true;

    private boolean isBackFromMapScanning = false;


    public boolean isBackFromMapScanning() {
        return isBackFromMapScanning;
    }

    public void setBackFromMapScanning(boolean backFromMapScanning) {
        isBackFromMapScanning = backFromMapScanning;
    }

    public String calcNextTestPoint() {
        return ++currentTestPoint + "";
    }

    public int getCurrentTestPoint() {
        return currentTestPoint;
    }

    public MapBuildingPresenter(MapBuildingContract.View view) {
        this.view = view;
    }

    @Override
    public void changeToConstructMap() {
        this.currentMode = 2;
        controller.modelMapping();
    }

    @Override
    public void changeToNavMode() {
        this.currentMode = 1;
        controller.modelNavi();
    }

    @Override
    public void saveMap() {
        this.currentMode = 1;
        controller.saveMap();
    }

    @Override
    public int getPointType() {
        return pointType;
    }

    @Override
    public void addPointCount() {
        pointCount++;
    }

    @Override
    public void resetPointCount() {
        pointCount = 1;
    }

    @Override
    public int getPointCount() {
        return pointCount;
    }

    @Override
    public int getCurrentNavMode() {
        return currentMode;
    }

    public void setCurrentMode(int currentMode) {
        this.currentMode = currentMode;
    }

    @Override
    public void onRebuildMapClicked(Context context) {
        VoiceHelper.play("voice_click_to_rebuild_map");
        EasyDialog.getInstance(context).confirm(context.getString(R.string.voice_click_to_rebuild_map), new EasyDialog.OnViewClickListener() {
            @Override
            public void onViewClick(Dialog dialog, int id) {
                dialog.dismiss();
                if (id == R.id.btn_confirm) {
                    VoiceHelper.play("voice_entering_map_building_mode");
                    changeToConstructMap();
                    view.onEnteringMapBuildingMode(context.getString(R.string.voice_entering_map_building_mode));
                }
            }
        });
    }

    @Override
    public void onAbandonMapBuildingClicked(Context context) {
        VoiceHelper.play("voice_click_to_abandon_map_building");
        EasyDialog.getInstance(context).confirm(context.getString(R.string.voice_click_to_abandon_map_building), new EasyDialog.OnViewClickListener() {
            @Override
            public void onViewClick(Dialog dialog, int id) {
                dialog.dismiss();
                if (id == R.id.btn_confirm) {
                    VoiceHelper.play("voice_entering_nav_mode");
                    changeToNavMode();
                    view.onEnteringNavMode(context.getString(R.string.voice_entering_nav_mode));
                }
            }
        });
    }

    @Override
    public void onSaveMapClicked(Context context) {
        VoiceHelper.play("voice_click_to_save_map");
        EasyDialog.getInstance(context).confirm(context.getString(R.string.voice_click_to_save_map), new EasyDialog.OnViewClickListener() {
            @Override
            public void onViewClick(Dialog dialog, int id) {
                dialog.dismiss();
                if (id == R.id.btn_confirm) {
                    VoiceHelper.play("voice_saving_map_and_entering_nav_mode");
                    resetPointCount();
                    saveMap();
                    view.onEnteringNavMode(context.getString(R.string.voice_saving_map_and_entering_nav_mode));
                }
            }
        });
    }

    @Override
    public void onMarkPointClicked(Context context, int id) {
        String prompt;
        if (id == R.id.tv_set_disinfection_point) {
            pointType = 1;
            prompt = context.getString(R.string.text_confirm_add_disinfection_point_at_this_location, pointCount);
            VoiceHelper.play("voice_confirm_add_disinfection_point_at_this_location");
        } else {
            pointType = 2;
            prompt = context.getString(R.string.voice_confirm_set_charging_pile_at_this_location);
            VoiceHelper.play("voice_confirm_set_charging_pile_at_this_location");
        }
        EasyDialog.getInstance(context).confirm(prompt, new EasyDialog.OnViewClickListener() {
            @Override
            public void onViewClick(Dialog dialog, int id) {
                dialog.dismiss();
                if (id == R.id.btn_confirm) {
                    controller.getCurrentPosition();
                }
            }
        });
    }

    @Override
    public void onMarkPoint(Context context, double[] position) {
        if (pointType == 1) {
            controller.markPoint(position, State.PointType.NORMAL, getPointCount() + "");
        } else {
            controller.markPoint(position, State.PointType.CHARGE, context.getString(R.string.text_charging_pile));
        }
    }

    @Override
    public void onExitDeployClicked(Context context) {
        VoiceHelper.play("voice_confirm_exit_deploy");
        EasyDialog.getInstance(context).confirm(context.getString(R.string.voice_confirm_exit_deploy), new EasyDialog.OnViewClickListener() {
            @Override
            public void onViewClick(Dialog dialog, int id) {
                dialog.dismiss();
                if (id == R.id.btn_confirm) {
                    view.onConfirmExitDeploy();
                }
            }
        });
    }

    @Override
    public void onDisinfectionRouteTestClicked(Context context) {
        VoiceHelper.play("voice_going_to_start_disinfection_route_test");
        EasyDialog.getInstance(context).confirm(context.getString(R.string.voice_going_to_start_disinfection_route_test), new EasyDialog.OnViewClickListener() {
            @Override
            public void onViewClick(Dialog dialog, int id) {
                dialog.dismiss();

                if (id == R.id.btn_confirm) {
                    //急停按下
                    if (nc.getEmergencyStop() == 0) {
                        VoiceHelper.play("voice_emergency_stop_turn_on");
                        return;
                    }

                    if (nc.isAcCharging()) {
                        VoiceHelper.play("voice_already_in_charging");
                        return;
                    }

                    isPointTest = true;
                    nc.navigationByPoint(calcNextTestPoint());
                }
            }
        });
    }

    @Override
    public void cancelTest() {
        currentTestPoint = 0;
        isPointTest = true;
        if (nc.isNavigating()) {
            controller.cancelNavigation();
        }
        if (nc.isChargingDocking()) {
            controller.cancelCharge();
            nc.setChargingDocking(false);
        }
    }

    @Override
    public void onPointFound(Context context) {
        //第一个目标点找到，弹出测试进度对话框
        if (currentTestPoint == 1) {
            EasyDialog.getInstance(context).onlyCancel(context.getString(R.string.text_navigating_to_target_point_for_test, currentTestPoint + ""), new EasyDialog.OnViewClickListener() {
                @Override
                public void onViewClick(Dialog dialog, int id) {
                    dialog.dismiss();
                    cancelTest();
                }
            });
            return;
        }
        //如果是消毒点位测试
        if (isPointTest) {
            EasyDialog.getInstance().update(R.id.tv_content, context.getString(R.string.text_navigating_to_target_point_for_test, currentTestPoint + ""));
        }
    }

    @Override
    public void onPowerConnected(Context context) {
        //测试结束
        if (!isPointTest) {
            showTestResult(context, context.getString(R.string.text_test_result_charging_docking_success));
        }
    }

    @Override
    public void onDockFailed(Context context) {
        showTestResult(context, context.getString(R.string.text_test_result_charging_docking_failed));
    }

    @Override
    public void deletePoint() {
        controller.deletePoint(pointType == 1 ? getPointCount() + "" : Constant.chargePoint.name);
    }

    @Override
    public void onNavigationStartResult(Context context, int code, String name) {
        if (code == -4) {
            if (Constant.chargePoint.name.equals(name)) {
                showTestResult(context, context.getString(R.string.text_test_result_charger_not_found));
                return;
            }
            if (currentTestPoint == 1) {
                //测试结束
                currentTestPoint = 0;
                EasyDialog.getInstance(context).warnError(context.getString(R.string.text_test_failed_for_not_found_start_point));
                return;
            }
            //其它情况去充电
            isPointTest = false;
            nc.navigationByPoint(Constant.chargePoint.name);
            //正在前往充电测试
            EasyDialog.getInstance().update(R.id.tv_content, context.getString(R.string.text_going_to_charging_pile_for_charge_test));
            return;
        }
        String navigationStartError = Errors.getNavigationStartError(context, code);
        if (!TextUtils.isEmpty(navigationStartError)) {
            EasyDialog.getInstance(context).warnError(Errors.getNavigationStartError(context, code));
            cancelTest();
        }
    }

    @Override
    public void onNavigationCompleteResult(Context context, int code, String name, float mileage) {
        if (code == 0) {
            if (context.getString(R.string.text_charging_pile).equals(name)) {
                nc.setChargingDocking(true);
                VoiceHelper.play("voice_start_docking_charging_pile");
                return;
            }
            nc.navigationByPoint(calcNextTestPoint());
            return;
        }
        list.add(name);
        nc.navigationByPoint(calcNextTestPoint());
    }

    @Override
    public void onSensorError(Context context, int code) {

    }

    @Override
    public void onEmergencyStopStateChange(Context context, int emergencyStopState) {
        //测试结束
        if (emergencyStopState == 0 && currentTestPoint != 0) {
            controller.cancelNavigation();
            controller.cancelCharge();
            currentTestPoint = 0;
            isPointTest = true;
            list.clear();
            if (EasyDialog.isShow()) EasyDialog.getInstance().dismiss();
            mHandler.postDelayed(() -> EasyDialog.getInstance(context).warnError(context.getString(R.string.text_test_result_canceled_by_user)), 500);
        }
    }

    private String getRouteResult(Context context) {
        return list.isEmpty() ? context.getString(R.string.text_route_test_success) : context.getString(R.string.text_route_test_failed, list.toString());
    }

    private void showTestResult(Context context, String result) {
        currentTestPoint = 0;
        isPointTest = true;
        String routeResult = getRouteResult(context);
        list.clear();
        if (EasyDialog.isShow()) EasyDialog.getInstance().dismiss();
        VoiceHelper.play("voice_route_test_finished");
        mHandler.postDelayed(() -> EasyDialog.getInstance(context).warnError(routeResult + "，" + result), 300);
    }
}
