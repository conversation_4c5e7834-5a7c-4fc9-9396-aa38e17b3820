package com.reeman.robot.disinfection.request;

import android.content.SharedPreferences;
import android.text.TextUtils;
import android.util.Log;

import com.elvishew.xlog.XLog;
import com.reeman.robot.disinfection.BuildConfig;
import com.reeman.robot.disinfection.constants.Constant;
import com.reeman.robot.disinfection.request.factory.ServiceFactory;
import com.reeman.robot.disinfection.request.model.LoginResponse;
import com.reeman.robot.disinfection.utils.SpManager;

import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.concurrent.TimeUnit;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLSession;

import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okio.Buffer;
import retrofit2.Retrofit;
import retrofit2.adapter.rxjava3.RxJava3CallAdapterFactory;
import retrofit2.converter.gson.GsonConverterFactory;

public class RetrofitClient {

    private static final Retrofit client;
    public static final int READ_TIME_OUT = 8;
    public static final int WRITE_TIME_OUT = 8;
    public static final int CONNECT_TIME_OUT = 5;
    public static final String BASE_URL = "http://navi.rmbot.cn/";
    private static OkHttpClient okHttpClient;

    private RetrofitClient() {

    }

    public static OkHttpClient getOkHttpClient() {
        return okHttpClient;
    }

    static {
        okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(CONNECT_TIME_OUT, TimeUnit.SECONDS)
                .readTimeout(READ_TIME_OUT, TimeUnit.SECONDS)
                .writeTimeout(WRITE_TIME_OUT, TimeUnit.SECONDS)
                .hostnameVerifier(new HostnameVerifier() {
                    @Override
                    public boolean verify(String hostname, SSLSession session) {
                        return true;
                    }
                })
                .addInterceptor(chain -> {
                    Request request = chain.request();
                    String method = request.method();
                    RequestBody body = request.body();
                    Log.w(BuildConfig.TAG, "\r\nStart************************************************************Start\r\n");
                    Log.w(BuildConfig.TAG, "http method: " + method);
                    Log.w(BuildConfig.TAG, "http url: " + request.url().toString());
                    Response response;
                    String accessToken = SpManager.getInstance().getString(Constant.ACCESS_TOKEN, null);
                    //有token带上token
                    if (accessToken != null) {
                        Headers headers = request.headers().newBuilder().add("Authorization", accessToken).build();
                        Request newRequest = request.newBuilder().headers(headers).build();
                        Log.w(BuildConfig.TAG, "headers: " + newRequest.headers());
                        response = chain.proceed(newRequest);
                    } else {
                        Log.w(BuildConfig.TAG, "headers: " + request.headers());
                        response = chain.proceed(request);
                    }
                    if (method.equalsIgnoreCase("post") && body != null) {
                        Log.w(BuildConfig.TAG, "body: " + getBodyString(body));
                    }
                    Log.w(BuildConfig.TAG, "response: " + response.toString());
                    Log.w(BuildConfig.TAG, "\r\nEnd***************************************************************End\r\n");

                    //token过期重新登录
                    if (response.code() == 401) {
                        String username = SpManager.getInstance().getString(Constant.USERNAME, "");
                        String password = SpManager.getInstance().getString(Constant.PASSWORD, "");
                        if (TextUtils.isEmpty(username) && TextUtils.isEmpty(password)) {
                            username = Constant.DEFAULT_USERNAME;
                            password = Constant.DEFAULT_PASSWORD;
                        }

                        XLog.w("token过期，重新登录");
                        HashMap<String, String> map = new HashMap<>();
                        map.put("account", username);
                        map.put("password", password);

                        //登录获取token
                        LoginResponse loginResponse = ServiceFactory.getRobotService().loginSync(map).execute().body();
                        if (loginResponse == null || loginResponse.data == null || loginResponse.data.result == null)
                            return response;
                        XLog.w("重新登录成功");
                        String newAccessToken = loginResponse.data.result.accessToken;
                        String newRefreshToken = loginResponse.data.result.refreshToken;
                        SharedPreferences.Editor edit = SpManager.getInstance().edit();
                        edit.putString(Constant.ACCESS_TOKEN, newAccessToken);
                        edit.putString(Constant.REFRESH_TOKEN, newRefreshToken);
                        edit.apply();
                        response.close();
                        Headers headers = request.headers().newBuilder().add("Authorization", newAccessToken).build();
                        Request newRequest = request.newBuilder().headers(headers).build();
                        return chain.proceed(newRequest);
                    }
                    return response;
                })
                .build();

        client = new Retrofit.Builder()
                .baseUrl(BASE_URL)
                .addConverterFactory(GsonConverterFactory.create())
                .addCallAdapterFactory(RxJava3CallAdapterFactory.create())
                .client(okHttpClient)
                .build();
    }

    private static String getBodyString(RequestBody requestBody) {
        String bodyString = null;
        if (requestBody != null) {
            Buffer buffer = new Buffer();
            try {
                requestBody.writeTo(buffer);
            } catch (IOException e) {
                e.printStackTrace();
            }
            MediaType contentType = requestBody.contentType();
            Charset charset = StandardCharsets.UTF_8;
            if (contentType != null) {
                charset = contentType.charset(charset);
            }
            bodyString = buffer.readString(charset);
        }
        return bodyString;
    }

    public static Retrofit getInstance() {
        return client;
    }

}
