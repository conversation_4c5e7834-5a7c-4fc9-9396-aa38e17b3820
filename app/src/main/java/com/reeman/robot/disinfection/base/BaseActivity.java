package com.reeman.robot.disinfection.base;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.os.Parcelable;
import android.provider.SyncStateContract;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import androidx.annotation.IdRes;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.elvishew.xlog.XLog;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.example.workomar.BuildConfig;
import com.example.workomar.R;
import com.reeman.robot.disinfection.activities.TaskExecutingActivity;
import com.reeman.robot.disinfection.constants.Constant;
import com.reeman.robot.disinfection.constants.State;
import com.reeman.robot.disinfection.event.RobotEvent;
import com.reeman.robot.disinfection.repository.entities.Task;
import com.reeman.robot.disinfection.request.RetrofitClient;
import com.reeman.robot.disinfection.request.factory.ServiceFactory;
import com.reeman.robot.disinfection.request.model.Point;
import com.reeman.robot.disinfection.request.model.TaskModel;
import com.reeman.robot.disinfection.utils.ScreenUtils;
import com.reeman.robot.disinfection.utils.SpManager;
import com.reeman.robot.disinfection.utils.VoiceHelper;
import com.reeman.robot.disinfection.widgets.EasyDialog;
import com.reeman.ros.event.Event;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.ConnectException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import io.reactivex.rxjava3.annotations.NonNull;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.ObservableEmitter;
import io.reactivex.rxjava3.core.ObservableOnSubscribe;
import io.reactivex.rxjava3.core.Observer;
import io.reactivex.rxjava3.disposables.Disposable;
import io.reactivex.rxjava3.functions.Consumer;
import io.reactivex.rxjava3.schedulers.Schedulers;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

import static com.reeman.robot.disinfection.base.BaseApplication.controller;
import static com.reeman.robot.disinfection.base.BaseApplication.nc;

public abstract class BaseActivity extends AppCompatActivity {

    private int dockFailedCount = 0;

    private static final int REQUEST_CODE_FOR_GUIDE = 1000;

    protected Handler mHandler = new Handler(Looper.getMainLooper());

    public static final List<Activity> activities = new ArrayList<>();


    protected final Runnable chargeRunnable = new Runnable() {
        @Override
        public void run() {
            try {
                if (nc != null && nc.getEmergencyStop() == 0) return;
                if (nc != null) {
                    nc.navigationByPoint(Constant.chargePoint.name);
                }
            } catch (Exception e) {
                XLog.e("Error in chargeRunnable", e);
            }
        }
    };

    //findViewById
    public <T extends View> T $(@IdRes int id) {
        return findViewById(id);
    }

    //是否隐藏底部导航栏，由子类决定
    protected boolean disableBottomNavigationBar() {
        return true;
    }

    //获取布局
    protected abstract int getLayoutRes();

    //是否应该响应电量低和定时任务事件
    protected boolean shouldResponse2TimeEvent() {
        return false;
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        activities.add(this);
        Log.w(BuildConfig.TAG, this + "onCreate");
        if (disableBottomNavigationBar()) {
            ScreenUtils.hideBottomUIMenu(this);
        } else {
            ScreenUtils.setImmersive(this);
        }
        setContentView(getLayoutRes());
        initView();
        initData();
    }

    protected void initData() {

    }

    protected void initView() {
    }

    @Override
    protected void onStart() {
        super.onStart();
        Log.w(BuildConfig.TAG, this + "onStart");
    }

    @Override
    protected void onResume() {
        super.onResume();
        EventBus.getDefault().register(this);
        Log.w(BuildConfig.TAG, this + "onResume");
    }

    @Override
    protected void onPause() {
        super.onPause();
        EventBus.getDefault().unregister(this);
        Log.w(BuildConfig.TAG, this + "onPause");
    }

    @Override
    protected void onStop() {
        super.onStop();
        Log.w(BuildConfig.TAG, this + "onStop");
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        activities.remove(this);
        Log.w(BuildConfig.TAG, this + "onDestroy");
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onWaypointUpdateEvent(Event.OnWaypointUpdateEvent event) {
        onWaypointUpdate();
    }

    protected void onWaypointUpdate() {
        Observable
                .create(new ObservableOnSubscribe<Point>() {
                    @Override
                    public void subscribe(@NonNull ObservableEmitter<Point> emitter) throws Throwable {
                        String hostIp = Event.getIpEvent().ipAddress;
                        retrofit2.Response<Map<String, List<Point>>> response;
                        try {
                            response = ServiceFactory.getRobotService().fetchPoints("http://" + hostIp + "/reeman/position").execute();
                            if (response.code() != 200) {
                                throw new ConnectException();
                            }
                            Map<String, List<Point>> map = response.body();
                            if (map == null || map.size() == 0) {
                                throw new ConnectException();
                            }
                            List<Point> waypoints = map.get("waypoints");
                            //将餐位缓存下来
                            if (waypoints != null && waypoints.size() > 0) {
                                XLog.w("waypoint ::" + waypoints);
                                boolean hasChangePoint = false;
                                for (Point waypoint : waypoints) {
                                    if (State.PointType.CHARGE.equals(waypoint.type)) {
                                        hasChangePoint = true;
                                        Constant.chargePoint = waypoint;
                                        Point.PoseDTO pose = waypoint.pose;
                                        Constant.chargePointCoordinate = new double[]{pose.x, pose.y, pose.theta};
                                        SpManager.getInstance().edit().putString(Constant.CHARGE_POINT, new Gson().toJson(waypoint)).apply();
                                        break;
                                    }
                                }
                                if (!hasChangePoint) {
                                    emitter.onError(new Exception());
                                }
                            } else {
                                emitter.onError(new Exception());
                            }
                        } catch (Exception e) {
                            emitter.onError(new Exception());
                        }
                        emitter.onNext(Constant.chargePoint);
                        emitter.onComplete();
                    }
                }).subscribeOn(Schedulers.io())
                .observeOn(Schedulers.io())
                .subscribe(new Consumer<Point>() {
                    @Override
                    public void accept(Point points) throws Throwable {

                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Throwable {
                        XLog.w("onerror=============");
                        Constant.chargePointCoordinate = new double[]{};

                    }
                });
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onSensorState(Event.OnCheckSensorsEvent event) {
        if (event.rawData.equals(nc.getLastSensorState())) return;
        nc.setLastSensorState(event.rawData);
        if (event.rawData.contains("0")) {
            onSensorsError(event);
        }
    }


    protected void onSensorsError(Event.OnCheckSensorsEvent event) {
//        Notifier.notify(new Msg(NotifyConstant.HARDWARE_NOTIFY, "传感器状态", "传感器异常:" + event.toString(), Event.getOnHostnameEvent().hostname));
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onCoreDataEvent(Event.OnCoreDataEvent event) {
        XLog.w("core_____" + event.rawData);
        onCoreData(event);
    }

    public void onCoreData(Event.OnCoreDataEvent event) {
        if (nc.getLevel() != event.battery || nc.getPlug() != event.charger) {
            onCustomBatteryChange();
            if (nc.getPlug() != event.charger) {
                if ((nc.getPlug() == 2 || nc.getPlug() == 3) && event.charger == 1) {
                    onCustomPowerDisconnected();
                } else if (event.charger == 2 || event.charger == 3) {
                    uploadLogs();
                    nc.setChargingDocking(false);
                    onCustomPowerConnected();
                } else if (event.charger == 8) {
                    nc.setChargingDocking(true);
                } else if (event.charger > 8) {
                    XLog.w("充电对接失败");
                    onPrivateDockFailed();
                }
                nc.setPlug(event.charger);
            }
            if (nc.getLevel() != event.battery) {
                nc.setLevel(event.battery);
            }
        }
        detailEmergencyStop(event.button);
        if (!nc.isFall() && (event.cliff == 4 || event.cliff == 2 || event.cliff == 1))
            onCustomDropEvent();
    }

    protected void onCustomDropEvent() {
    }

    protected void detailEmergencyStop(int button) {
        if (nc.getEmergencyStop() == -1) {
            nc.setEmergencyStop(button);
            return;
        }
        if (nc.getEmergencyStop() != button) {
            if (button == 0) {
                if (nc.isNavigating()) controller.cancelNavigation();
                if (nc.isChargingDocking()) controller.cancelCharge();
                nc.setChargingDocking(false);
                mHandler.removeCallbacks(chargeRunnable);
            }
            nc.setEmergencyStop(button);
            onCustomEmergencyStopStateChange(button);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onNaviResult(Event.OnNavResultEvent event) {
        if (event.state == State.NavigationState.FAILURE
                || event.state == State.NavigationState.PAUSE
                || event.state == State.NavigationState.CANCEL
                || event.state == State.NavigationState.COMPLETE) {
            nc.setNavigating(false);
        }
        if (event.state == State.NavigationState.RECEIVE) {
            onNavigationStartResult(event.code, event.name);
            return;
        }
        if (event.state == State.NavigationState.COMPLETE) {
            onNavigationCompleteResult(event.code, event.name, event.mileage);
            return;
        }
        if (event.state == State.NavigationState.CANCEL) {
            onNavigationCancelResult(event.code);
            return;
        }
    }

    protected void onNavigationCancelResult(int code) {
        nc.setNavigating(false);
    }

    protected void onNavigationCompleteResult(int code, String name, float mileage) {
        if (code == 0) nc.setNavigating(false);
        if (Constant.chargePoint.name.equals(name)) {
            nc.setChargingDocking(true);
            VoiceHelper.play("voice_start_docking_charging_pile");
            return;
        }
        if (EasyDialog.isShow()) EasyDialog.getInstance().dismiss();
    }

    protected void onNavigationStartResult(int code, String name) {
        if (code != 0) {
            nc.setNavigating(false);
            if (code == -4) {
                if (Constant.chargePoint.name.equals(name)) {
                    if (EasyDialog.isShow()) EasyDialog.getInstance().dismiss();
                    onCustomChargingPileNotFound();
                } else {
                    onCustomPointNotFoundEvent(name);
                }
            } else if (code == -7) {
//                onCustomPathCanNotArrive();
            } else {
                onStartNavFailed(code);
            }
        }
    }

//    //充电对接失败
//    @Subscribe(threadMode = ThreadMode.MAIN)
//    public void onDockFailed(RobotEvent.OnDockFailedEvent event) {
//        XLog.w("充电对接失败");
//        onPrivateDockFailed();
//    }

//    //未找到充电桩
//    @Subscribe(threadMode = ThreadMode.MAIN)
//    public void onChargingPileNotFoundEvent(RobotEvent.OnChargeChargingPileNotFoundEvent event) {
//        controller.setNavigating(false);
//        XLog.w("未找到充电桩");
//        if (EasyDialog.isShow()) EasyDialog.getInstance().dismiss();
//        onCustomChargingPileNotFound();
//    }

//    //电量变化，电源连接断开
//    @Subscribe(threadMode = ThreadMode.MAIN)
//    public void onPowerEvent(RobotEvent.OnPowerEvent event) {
//        Intent powerIntent = event.powerIntent;
//        switch (powerIntent.getAction()) {
//            case Intent.ACTION_BATTERY_CHANGED:
//                onCustomBatteryChange(powerIntent);
//                break;
//            case Intent.ACTION_POWER_CONNECTED:
//                uploadLogs();
//                controller.setChargingDocking(false);
//                onCustomPowerConnected();
//                break;
//            case Intent.ACTION_POWER_DISCONNECTED:
//                onCustomPowerDisconnected();
//                break;
//        }
//    }

    protected void uploadLogs() {
        File root = new File(Environment.getExternalStorageDirectory() + "/disinfection_robot");
        if (!root.exists()) return;
        File[] files = root.listFiles();
        if (files == null || files.length == 0) return;
        JsonArray jsonArray = new JsonArray();
        MultipartBody.Builder body = new MultipartBody.Builder().setType(MultipartBody.FORM);
        JsonObject singleParam;
        for (File file : files) {
            if (file.exists()) {
                singleParam = new JsonObject();
                singleParam.addProperty("project", "新版消毒");
                singleParam.addProperty("device", Event.getOnHostnameEvent().hostname);
                singleParam.addProperty("log", file.getName());
                singleParam.addProperty("file", file.getName());
                jsonArray.add(singleParam);
                body.addFormDataPart("uploadfiles", file.getName(), RequestBody.create(MediaType.parse("file/file"), file));
            }
        }
        JsonObject params = new JsonObject();
        params.addProperty("files", jsonArray.toString());
        RequestBody requestBody = body.addFormDataPart("information", params.toString()).build();
        Request request = new Request.Builder()
                .url("http://navi.rmbot.cn/logfile/uploadfiles")
                .post(requestBody)
                .build();
        OkHttpClient okHttpClient = RetrofitClient.getOkHttpClient();
        okHttpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(@NonNull Call call, @NonNull IOException e) {
                Log.w("日志上报：", e.toString());
            }

            @Override
            public void onResponse(@NonNull Call call, @NonNull Response response) throws IOException {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
                String todayFileName = sdf.format(new Date());
                for (File file : files) {
                    if (todayFileName.equals(file.getName())) continue;
                    file.delete();
                }
            }
        });

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onStartTaskEvent(Event.OnStartTaskEvent event) {
        if (!shouldResponse2RemoteTaskEvent()) return;
        onCustomStartTaskEvent(new Gson().fromJson(event.taskStr, TaskModel.class));
    }

    protected void onCustomStartTaskEvent(TaskModel detail) {
        if (nc.isChargingDocking() ||
                nc.isNavigating() ||
                nc.isAcCharging() ||
                nc.getEmergencyStop() == 0)
            return;
        if (EasyDialog.isShow()) EasyDialog.getInstance().dismiss();
        VoiceHelper.play("voice_going_to_start_disinfection_task");
        Intent intent = new Intent(this, TaskExecutingActivity.class);
        intent.putExtra("extra", Task.getByRemoteCommand(detail));
        startActivity(intent);
    }

    protected boolean shouldResponse2RemoteTaskEvent() {
        return false;
    }


//    //急停开关状态改变
//    @Subscribe(threadMode = ThreadMode.MAIN)
//    public void onEmergencyStopStateObtained(RobotEvent.OnEmergencyStopEvent event) {
//        XLog.w("急停开关状态：" + event.emergencyStopState);
//        if (event.emergencyStopState == 0) {
//            controller.setChargingDocking(false);
//            controller.setNavigating(false);
//            mHandler.removeCallbacks(chargeRunnable);
//        }
//        onCustomEmergencyStopStateChange(event.emergencyStopState);
//    }

//    //导航结果
//    @Subscribe(threadMode = ThreadMode.MAIN)
//    public void onNavResEvent(RobotEvent.OnNavResEvent event) {
//        controller.setNavigating(false);
//        onCustomNavResEvent(event);
//    }

//    @Subscribe(threadMode = ThreadMode.MAIN)
//    public void onPointNotFoundEvent(RobotEvent.OnPointNotFoundEvent event) {
//        controller.setNavigating(false);
//
//        onCustomPointNotFoundEvent(event);
//    }

    //子类自定义目标点找不到处理逻辑
    protected void onCustomPointNotFoundEvent(String point) {

    }

    protected void onStartNavFailed(int code) {

    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onShutDown(Event.OnPowerOffEvent event) {
        shutdown();
    }

    /**
     * root生效
     */
    protected void shutdown() {
        try {
            Process process = Runtime.getRuntime().exec("reboot -p");
            String data = null;
            BufferedReader errorLine = new BufferedReader(new InputStreamReader(process.getErrorStream()));
            BufferedReader inputLine = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String error = null;
            while ((error = errorLine.readLine()) != null && !error.equals("null")) {
                data += error + "\n";
            }
            String input = null;
            while ((input = inputLine.readLine()) != null && !input.equals("null")) {
                data += input + "\n";
            }
        } catch (Exception e) {

        }

    }

//    //子类自定义导航结果处理逻辑，否则由父类同一处理
//    protected void onCustomNavResEvent(RobotEvent.OnNavResEvent event) {
//        try {
//            String result = event.rawData.replace("nav_res:", "");
//            JSONObject resultJson = new JSONObject(result);
//            int res = resultJson.optInt("res");
//            String dest = resultJson.optString("dest");
//            String sensor = resultJson.optString("sensor");
//            int reason = resultJson.optInt("reason");
//            if (!getString(R.string.text_charging_pile).equals(dest)) {
//                return;
//            }
//            if (res == 0) {
//                controller.setChargingDocking(true);
//                VoiceHelper.play("voice_start_docking_charging_pile");
//                return;
//            }
//            if (EasyDialog.isShow()) EasyDialog.getInstance().dismiss();
//            if (reason == 1) {
//                EasyDialog.getInstance(this).warnError(Errors.getHardwareError(this, sensor));
//            } else {
//                EasyDialog.getInstance(this).warnError(Errors.getErrorByCode(this, reason));
//            }
//        } catch (JSONException e) {
//            e.printStackTrace();
//        }
//    }

    private void onPrivateDockFailed() {
        if (++dockFailedCount < 2) {
            VoiceHelper.play("voice_charging_docking_failed_and_re_dock");
            mHandler.postDelayed(chargeRunnable, 4000);
            return;
        }
        dockFailedCount = 0;
        nc.setNavigating(false);
        nc.setChargingDocking(false);
        onCustomDockFailed();
    }

    //子类自定义充电对接失败处理逻辑
    protected void onCustomDockFailed() {
        if (EasyDialog.isShow()) EasyDialog.getInstance().dismiss();
        VoiceHelper.play("voice_charging_dock_failed");
        EasyDialog.getInstance(this).warnError(getString(R.string.voice_charging_dock_failed));
    }

    //子类自定义急停开关处理逻辑，默认关闭提示窗口
    protected void onCustomEmergencyStopStateChange(int emergencyStopState) {
        if (EasyDialog.isShow()) EasyDialog.getInstance().dismiss();
        controller.cancelNavigation();
        controller.cancelCharge();
    }

    //子类自定义充电处理逻辑，默认关闭充电对接提醒
    protected void onCustomPowerConnected() {
        //VoiceHelper.play("voice_start_charging");
        if (EasyDialog.isShow()) EasyDialog.getInstance().dismiss();
    }

    //子类自定义断电处理逻辑
    protected void onCustomPowerDisconnected() {
        //VoiceHelper.play("voice_stop_charge");
    }

    //子类自定义电量变化处理逻辑
    protected void onCustomBatteryChange() {

    }

    //子类自定义找不到充电桩处理逻辑
    protected void onCustomChargingPileNotFound() {
        VoiceHelper.play("voice_not_found_charging_pile");
        EasyDialog.getInstance(this).warnError(getString(R.string.voice_not_found_charging_pile));
    }

    //子类自定义低电、定时任务处理逻辑
    protected void onCustomTimeStamp(RobotEvent.OnTimeEvent event) {
        //开始执行任务之前倒计时
        EasyDialog.getInstance(this).warnWithScheduledUpdateDetail(
                getString(Constant.RESPONSE_TIME, event),
                R.string.text_start_right_now,
                R.string.text_cancel_task,
                new EasyDialog.OnViewClickListener() {
                    @Override
                    public void onViewClick(Dialog dialog, int id) {
                        dialog.dismiss();
                        if (id == R.id.btn_confirm) startTask(event);
                    }
                },
                new EasyDialog.OnTimeStampListener() {
                    @Override
                    public void onTimestamp(TextView title, TextView content, Button cancelBtn, Button neutralBtn, Button confirmBtn, int current) {
                        content.setText(BaseActivity.this.getString(Constant.RESPONSE_TIME - current, event));
                    }

                    @Override
                    public void onTimeOut(EasyDialog dialog) {
                        dialog.dismiss();
                        startTask(event);
                    }
                },
                1000,
                Constant.RESPONSE_TIME * 1000
        );
    }


    //电量事件，定时任务
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onTimeStamp(RobotEvent.OnTimeEvent event) {
        //子类不想处理这些事件，返回
        XLog.w("检测是否符合定时任务状态： isNavigating: " + nc.isNavigating() + " isChargingDocking: " + nc.isChargingDocking());
        if (!shouldResponse2TimeEvent()) return;

        //现在在任务执行界面只处理低电
        if (activities.get(activities.size() - 1) instanceof TaskExecutingActivity) {
            onCustomTimeStamp(event);
            return;
        }

        //其它界面如果在导航中或者在充电对接中不处理
        if (nc.isNavigating() || nc.isChargingDocking()) return;
        onCustomTimeStamp(event);
    }

    //开始任务（充电或者开始定时任务）
    private void startTask(RobotEvent.OnTimeEvent event) {
        if (event.eventType == 0) {
            XLog.w("低电触发充电");
            nc.navigationByPoint(Constant.chargePoint.name);
            EasyDialog
                    .getInstance(this)
                    .onlyCancel(getString(R.string.text_going_to_charge), new EasyDialog.OnViewClickListener() {
                        @Override
                        public void onViewClick(Dialog dialog, int id) {
                            dialog.dismiss();
                            if (nc.isNavigating()) {
                                controller.cancelNavigation();
                            }
                            if (nc.isChargingDocking()) {
                                controller.cancelCharge();
                                nc.setChargingDocking(false);
                            }
                        }
                    });
        } else {
            if (nc.isChargingDocking()) {
                controller.cancelCharge();
                nc.setChargingDocking(false);
            }
            XLog.w("触发定时任务：" + event.task.toString());
            start(this, TaskExecutingActivity.class, event.task);
        }
    }

    private String getString(int secondsUntilFinished, RobotEvent.OnTimeEvent event) {
        String prompt;
        if (event.eventType == 0) {
            //充电事件
            prompt = getString(R.string.text_going_to_charge_for_low_power, secondsUntilFinished);
        } else {
            //定时任务
            prompt = getString(R.string.text_going_to_exec_schedule_task, secondsUntilFinished, event.task.taskName);
        }
        return prompt;
    }

    public static void start(Context context, Class<? extends Activity> clazz) {
        start(context, clazz, null);
    }

    public static void start(Context context, Class<? extends Activity> clazz, Parcelable extra) {
        Intent intent = new Intent(context, clazz);
        if (extra != null) {
            intent.putExtra(Constant.EXTRA, extra);
        }
        context.startActivity(intent);
    }

    public static void start(Activity context, Class<? extends Activity> clazz, String from, int extra) {
        Intent intent = new Intent(context, clazz);
        intent.putExtra(Constant.EXTRA, from);
        intent.putExtra(Constant.EXTRA_INT, extra);
        context.startActivityForResult(intent, REQUEST_CODE_FOR_GUIDE);
    }

    //结束所有Activity
    public void finishAll() {
        nc.reset();
        controller.stopListen();
        for (Activity activity : activities) {
            activity.finish();
        }
    }

    //充电时按下物理按键前进一段距离
    @Override
    public boolean onKeyUp(int keyCode, KeyEvent event) {
        if (event.getKeyCode() == KeyEvent.KEYCODE_F2 && nc.getPlug() == 2) {
            controller.moveForward();
        }
        return super.onKeyUp(keyCode, event);
    }
}
