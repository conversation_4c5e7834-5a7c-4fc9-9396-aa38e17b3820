package com.reeman.robot.disinfection.utils;

import android.content.Context;
import android.content.SharedPreferences;

import com.reeman.robot.disinfection.constants.Constant;
import com.reeman.robot.disinfection.R;

/**
 * @ClassName: SpManager.java
 * @Author: <PERSON><PERSON><PERSON><PERSON>(<EMAIL>)
 * @Date: 2022/1/9 15:03
 * @Description: sp存储工具类
 */
public class SpManager {
    private static SharedPreferences sSharedPreferences;

    public static void init(Context context) {
        sSharedPreferences = context.getSharedPreferences(Constant.SP_NAME, Context.MODE_PRIVATE);
    }

    public static SharedPreferences getInstance() {
        return sSharedPreferences;
    }

}
