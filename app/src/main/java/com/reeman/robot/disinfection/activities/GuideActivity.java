package com.reeman.robot.disinfection.activities;

import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.graphics.Rect;
import android.graphics.drawable.ColorDrawable;
import android.view.View;
import android.view.ViewTreeObserver;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.reeman.robot.disinfection.R;
import com.reeman.robot.disinfection.SplashActivity;
import com.reeman.robot.disinfection.base.BaseActivity;
import com.reeman.robot.disinfection.constants.Constant;
import com.reeman.robot.disinfection.utils.ScreenUtils;
import com.reeman.robot.disinfection.utils.SpManager;
import com.reeman.robot.disinfection.utils.ToastUtils;
import com.reeman.robot.disinfection.utils.VoiceHelper;
import com.sxu.shadowdrawable.ShadowDrawable;

import java.util.Arrays;
import java.util.List;

public class GuideActivity extends BaseActivity implements View.OnClickListener {

    private List<View> choices;
    private int currentStep;
    private String from;
    private TextView tvBack;
    private List<String> prompts;
    private List<Class<? extends BaseActivity>> classes;

    @Override
    protected boolean disableBottomNavigationBar() {
        return true;
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        if (hasFocus) {
            ScreenUtils.hideBottomUIMenu(this);
        }
    }

    public static void startActivity(Context context, String from) {
        Intent intent = new Intent(context, GuideActivity.class);
        intent.putExtra(Constant.EXTRA, from);
        context.startActivity(intent);
    }

    @Override
    protected int getLayoutRes() {
        return R.layout.activity_guide;
    }

    @Override
    protected void initView() {
        tvBack = $(R.id.tv_back);
        TextView tvExitApp = $(R.id.tv_exit_app);
        TextView tvSelectRobotType = $(R.id.tv_select_robot_type);
        TextView tvConnectWiFi = $(R.id.tv_connect_wifi);
        TextView tvLogin = $(R.id.tv_login);
        TextView tvEnterApp = $(R.id.tv_enter_app);
        tvBack.setOnClickListener(this);
        tvExitApp.setOnClickListener(this);
        tvSelectRobotType.setOnClickListener(this);
        tvConnectWiFi.setOnClickListener(this);
        tvLogin.setOnClickListener(this);
        tvEnterApp.setOnClickListener(this);

        choices = Arrays.asList(tvSelectRobotType, tvConnectWiFi, tvLogin, tvEnterApp);
    }

    @Override
    protected void initData() {
        //如果从闪屏界面进入引导，则隐藏返回按钮
        if (SplashActivity.class.getSimpleName().equals(from)) {
            tvBack.setVisibility(View.INVISIBLE);
        }
        prompts = Arrays.asList("voice_select_robot_type", "voice_please_connect_wifi", "voice_please_login", "voice_please_enter_app");
        classes = Arrays.asList(RobotTypeChooseActivity.class, WiFiConnectActivity.class, LoginActivity.class, MainActivity.class);
        from = getIntent().getStringExtra(Constant.EXTRA);
        currentStep = SpManager.getInstance().getInt(Constant.CURRENT_GUIDE_STEP, 1);
        if (SplashActivity.class.getSimpleName().equals(from)) {
            tvBack.setVisibility(View.INVISIBLE);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        for (int i = 0; i < choices.size(); i++) {
            View view = choices.get(i);
            if (i + 1 == currentStep) {
                VoiceHelper.play(prompts.get(i));
                ShadowDrawable.setShadowDrawable(view, Color.parseColor("#44FFFFFF"), 8, Color.parseColor("#44FFFFFF"), 15, 0, 0);
            } else {
                ShadowDrawable.setShadowDrawable(view, new ColorDrawable(Color.TRANSPARENT));
            }
        }
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        switch (id) {
            case R.id.tv_back:
                finish();
                break;
            case R.id.tv_exit_app:
                finishAll();
                break;
            case R.id.tv_select_robot_type:
            case R.id.tv_login:
            case R.id.tv_connect_wifi:
            case R.id.tv_enter_app:
                onChoiceSelect(v);
                break;
        }
    }

    private void onChoiceSelect(View view) {
        int index = choices.indexOf(view) + 1;
        //如果当前点击的item大于应该被引导的item，则提示应该先完成之前的引导
        if (index > currentStep) {
            String prompt = prompts.get(currentStep - 1);
            VoiceHelper.play(prompt);
            ToastUtils.showShortToast(getString(getResources().getIdentifier(prompt, "string", getPackageName())));
            return;
        }

        //当前点击的item小于等于应该被引导的item，则跳转到指定item界面
        int extra;
        if (index == currentStep) {
            extra = 1;
        } else {
            extra = 0;
        }

        Class<? extends BaseActivity> clazz = classes.get(index - 1);
        if (index == 4) {
            //引导完成，进入App
            Intent intent = new Intent(this, MainActivity.class);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
            startActivity(intent);
        } else {
            //进入指定引导Item
            BaseActivity.start(this, clazz, getClass().getSimpleName(), extra);
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        currentStep += resultCode;
        SharedPreferences.Editor edit = SpManager.getInstance().edit();
        edit.putInt(Constant.CURRENT_GUIDE_STEP, currentStep);
        edit.apply();
    }
}
