package com.reeman.robot.disinfection;

import com.example.workomar.BuildConfig;
import com.example.workomar.R;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.app.Dialog;
import android.content.Context;
import android.content.SharedPreferences;
import android.media.AudioManager;
import android.os.Handler;
import android.os.Looper;
import android.view.animation.LinearInterpolator;
import android.widget.ImageView;

import com.elvishew.xlog.LogLevel;
import com.elvishew.xlog.XLog;
import com.google.gson.Gson;
import com.reeman.robot.disinfection.activities.GuideActivity;
import com.reeman.robot.disinfection.activities.LanguageChooseActivity;
import com.reeman.robot.disinfection.activities.MainActivity;
import com.reeman.robot.disinfection.base.BaseActivity;
import com.reeman.robot.disinfection.constants.BuglyConstants;
import com.reeman.robot.disinfection.constants.Constant;
import com.reeman.robot.disinfection.controller.NavigationController;
import com.reeman.robot.disinfection.request.model.Point;
import com.reeman.robot.disinfection.utils.LocaleUtil;
import com.reeman.robot.disinfection.utils.SpManager;
import com.reeman.robot.disinfection.widgets.EasyDialog;
import com.reeman.ros.controller.RobotActionController;
import com.reeman.ros.event.Event;

import static com.reeman.robot.disinfection.base.BaseApplication.controller;
import static com.reeman.robot.disinfection.base.BaseApplication.mApp;
import static com.reeman.robot.disinfection.base.BaseApplication.nc;

import io.reactivex.rxjava3.annotations.NonNull;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.Observer;
import io.reactivex.rxjava3.disposables.Disposable;
import io.reactivex.rxjava3.schedulers.Schedulers;

public class SplashActivity extends BaseActivity {

    private ImageView ivLoading;
    private boolean receiveCoreData = false;

    @Override
    protected boolean disableBottomNavigationBar() {
        return false;
    }

    @Override
    protected int getLayoutRes() {
        return R.layout.activity_splash;
    }

    @Override
    protected void initView() {
        ivLoading = $(R.id.iv_loading);
    }

    @Override
    protected void initData() {
        int languageType = SpManager.getInstance().getInt(Constant.KEY_LANGUAGE_TYPE, Constant.DEFAULT_LANGUAGE_TYPE);
        if (languageType != -1 && languageType != LocaleUtil.getLocaleType()) {
            LocaleUtil.changeAppLanguage(getResources(), languageType);
            LocaleUtil.changeAppLanguage(mApp.getResources(), languageType);
            BuglyConstants.updateBuglyStrings(getResources());
        }

        int volume = SpManager.getInstance().getInt(Constant.SYS_VOLUME, Constant.DEFAULT_MEDIA_VOLUME);
        AudioManager audioManager = (AudioManager) getSystemService(Context.AUDIO_SERVICE);
        audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, volume, 0);
        audioManager.setStreamVolume(AudioManager.STREAM_NOTIFICATION, 0, 0);
    }

    @Override
    protected void onResume() {
        super.onResume();
        startLoadingAnimation();
        Event.reset();

        // Delay ROS initialization to avoid crashes
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            initializeRosWithRetry(0);
        }, 1000); // 1 second delay
    }

    private void initializeRosWithRetry(int attemptCount) {
        try {
            controller = RobotActionController.getInstance();
            nc = NavigationController.getInstance();
            controller.init(this, BuildConfig.DEBUG ? LogLevel.ALL : LogLevel.WARN, BuildConfig.APP_LOG_DIR);

            // If successful, continue with normal flow
            XLog.d("ROS initialization successful");

        } catch (Exception e) {
            XLog.e("ROS initialization failed, attempt: " + (attemptCount + 1), e);

            if (attemptCount < 2) { // Retry up to 3 times
                // Retry after 2 seconds
                new Handler(Looper.getMainLooper()).postDelayed(() -> {
                    initializeRosWithRetry(attemptCount + 1);
                }, 2000);
            } else {
                // Show detailed error message after all retries failed
                String errorMessage = getString(R.string.text_communicate_failed_with_ros) +
                    "\n\nError details: " + e.getMessage() +
                    "\n\nTroubleshooting:\n" +
                    "1. Check if ROS navigation system is running\n" +
                    "2. Verify network connection\n" +
                    "3. Restart the application\n" +
                    "4. Contact technical support if problem persists";

                EasyDialog.getInstance(SplashActivity.this).warn(errorMessage, (dialog, id) -> {
                    dialog.dismiss();
                    finish();
                });
            }
        }
    }

    private void startLoadingAnimation() {
        ObjectAnimator animator = ObjectAnimator.ofFloat(ivLoading, "rotation", 0, 360);
        animator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                if (receiveCoreData) {
                    receiveCoreData = false;
                    onSplashAnimationEnd();
                } else {
                    EasyDialog.getInstance(SplashActivity.this).warn(getString(R.string.text_communicate_failed_with_ros), new EasyDialog.OnViewClickListener() {
                        @Override
                        public void onViewClick(Dialog dialog, int id) {
                            dialog.dismiss();
                            finish();
                        }
                    });
                }

            }
        });
        animator.setDuration(1000);
        animator.setRepeatCount(3);
        animator.setInterpolator(new LinearInterpolator());
        animator.setRepeatMode(ValueAnimator.RESTART);
        animator.start();
    }

    private void onSplashAnimationEnd() {
        //如果没有引导过选择语言，跳转语言选择界面
        if (!SpManager.getInstance().getBoolean(Constant.IS_LANGUAGE_CHOSEN, Constant.DEFAULT_LANGUAGE_CHOSEN)) {
            LanguageChooseActivity.startActivity(this, getClass().getSimpleName());
            finish();
            return;
        }

        //如果引导过选择语言，没走完引导流程，则跳转引导界面
        if (SpManager.getInstance().getInt(Constant.CURRENT_GUIDE_STEP, Constant.DEFAULT_GUIDE_STEP) < Constant.GUIDE_COUNT) {
            GuideActivity.startActivity(this, getClass().getSimpleName());
            finish();
            return;
        }

        //语言选择过， 引导完成，跳转主界面
        BaseActivity.start(this, MainActivity.class, null);
        finish();
    }

    @Override
    public void onCoreData(Event.OnCoreDataEvent event) {
        super.onCoreData(event);
        receiveCoreData = true;
        controller.cpuPerformance();
        Observable.create(emitter -> {
                    Gson gson = new Gson();
                    SharedPreferences instance = SpManager.getInstance();
                    String chargePointJsonStr = instance.getString(Constant.CHARGE_POINT, null);
                    if (chargePointJsonStr != null) {
                        Constant.chargePoint = gson.fromJson(chargePointJsonStr, Point.class);
                        Point.PoseDTO pose = Constant.chargePoint.pose;
                        Constant.chargePointCoordinate = new double[]{pose.x, pose.y, pose.theta};
                    } else {
                        Constant.chargePoint = new Point(getString(R.string.text_charging_pile));
                        Constant.chargePointCoordinate = new double[]{};
                    }
                    emitter.onComplete();
                }).subscribeOn(Schedulers.io())
                .observeOn(Schedulers.io())
                .subscribe(new Observer<Object>() {
                    @Override
                    public void onSubscribe(@NonNull Disposable d) {

                    }

                    @Override
                    public void onNext(@NonNull Object o) {

                    }

                    @Override
                    public void onError(@NonNull Throwable e) {

                    }

                    @Override
                    public void onComplete() {

                    }
                });
    }
}
