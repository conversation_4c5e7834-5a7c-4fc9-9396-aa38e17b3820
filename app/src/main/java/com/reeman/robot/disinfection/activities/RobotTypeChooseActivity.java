package com.reeman.robot.disinfection.activities;

import static com.reeman.robot.disinfection.base.BaseApplication.nc;

import android.content.SharedPreferences;
import android.view.View;
import android.widget.Button;
import android.widget.RadioGroup;

import com.example.workomar.R;
import com.reeman.robot.disinfection.base.BaseActivity;
import com.reeman.robot.disinfection.constants.Constant;
import com.reeman.robot.disinfection.utils.SpManager;


public class RobotTypeChooseActivity extends BaseActivity implements View.OnClickListener {

    private RadioGroup rgRobotTypeControl;
    private String from;
    private int result;

    @Override
    protected int getLayoutRes() {
        return R.layout.activity_robot_type_choose;
    }

    @Override
    protected void initView() {
        rgRobotTypeControl = $(R.id.rg_robot_type_control);
        Button btnConfirm = $(R.id.btn_confirm);
        btnConfirm.setOnClickListener(this);
    }

    @Override
    protected void initData() {
        from = getIntent().getStringExtra(Constant.EXTRA);
        result = getIntent().getIntExtra(Constant.EXTRA_INT, 0);
    }

    @Override
    public void onClick(View v) {
        onSave();
    }

    private void onSave() {
        SharedPreferences.Editor edit = SpManager.getInstance().edit();
        int robotType;
        robotType = rgRobotTypeControl.getCheckedRadioButtonId() == R.id.rb_atomization_disinfection_robot ? 1 : 2;
        if (robotType == 2)
            robotType = rgRobotTypeControl.getCheckedRadioButtonId() == R.id.rb_ultraviolet_disinfection_robot ? 2 : 3;
        edit.putInt(Constant.ROBOT_TYPE, robotType);
        edit.putBoolean(Constant.OPEN_DETECTION,robotType == 3);
        edit.apply();
        if (GuideActivity.class.getSimpleName().equals(from)) {
            setResult(result);
        }
        finish();
    }
}
