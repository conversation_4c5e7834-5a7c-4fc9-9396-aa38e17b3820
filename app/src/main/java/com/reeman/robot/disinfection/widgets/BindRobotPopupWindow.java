package com.reeman.robot.disinfection.widgets;

import android.app.Activity;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.text.Editable;
import android.text.TextWatcher;
import android.text.method.HideReturnsTransformationMethod;
import android.text.method.PasswordTransformationMethod;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.GridLayout;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.google.android.material.textfield.TextInputEditText;
import com.example.workomar.R;
import com.reeman.robot.disinfection.utils.QRCodeUtils;

public class BindRobotPopupWindow extends PopupWindow {

    public BindRobotPopupWindow(Activity context) {
        setWidth(ViewGroup.LayoutParams.MATCH_PARENT);
        setHeight(ViewGroup.LayoutParams.WRAP_CONTENT);
        setFocusable(false);
        setBackgroundDrawable(new ColorDrawable(Color.WHITE));
        setOutsideTouchable(true);
        setAnimationStyle(R.style.popupWindowTranslateAnimation);
        ViewGroup root = (ViewGroup) LayoutInflater.from(context).inflate(R.layout.layout_qrcode, null);
        ImageView ivQrCode = root.findViewById(R.id.iv_qrcode);
        String qrCodeStr = QRCodeUtils.getQrCodeStr();
        ivQrCode.setImageBitmap(QRCodeUtils.createQRCodeBitmap(qrCodeStr, 300, 300, "UTF-8", "H", "1", Color.BLACK, Color.WHITE));
        setContentView(root);
    }

}
