package com.reeman.robot.disinfection.presenter.impl;

import android.content.Context;
import android.content.SharedPreferences;
import android.media.AudioManager;
import android.text.Editable;
import android.text.TextUtils;
import android.widget.EditText;

import com.microsoft.cognitiveservices.speech.ResultReason;
import com.microsoft.cognitiveservices.speech.SpeechConfig;
import com.microsoft.cognitiveservices.speech.SpeechSynthesisOutputFormat;
import com.microsoft.cognitiveservices.speech.SpeechSynthesisResult;
import com.microsoft.cognitiveservices.speech.SpeechSynthesizer;
import com.microsoft.cognitiveservices.speech.audio.AudioConfig;
import com.example.workomar.R;
import com.reeman.robot.disinfection.activities.LoginActivity;
import com.reeman.robot.disinfection.base.BaseActivity;
import com.reeman.robot.disinfection.constants.Constant;
import com.reeman.robot.disinfection.contract.SettingContract;
import com.reeman.robot.disinfection.utils.LocaleUtil;
import com.reeman.robot.disinfection.utils.SpManager;
import com.reeman.robot.disinfection.utils.ToastUtils;
import com.reeman.robot.disinfection.widgets.SetLockScreenPopupWindow;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.annotations.NonNull;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.ObservableEmitter;
import io.reactivex.rxjava3.core.ObservableOnSubscribe;
import io.reactivex.rxjava3.core.Observer;
import io.reactivex.rxjava3.disposables.Disposable;
import io.reactivex.rxjava3.schedulers.Schedulers;

import static com.reeman.robot.disinfection.base.BaseApplication.controller;
import static com.reeman.robot.disinfection.base.BaseApplication.nc;

public class SettingPresenter implements SettingContract.Presenter {

    private final SettingContract.View view;
    private int confirmCount;
    private String firstEnteredPassword;

    public SettingPresenter(SettingContract.View view) {
        this.view = view;
    }

    @Override
    public void persist(Context context, int voiceBroadcast, int lockScreen,int openDetection, String navSpeed, int lowPower, int volume, String delay) {
        if (lowPower < 10 || lowPower > 80) {
            view.showInvalidLowPower();
            return;
        }
        if (TextUtils.isEmpty(delay)) {
            view.showInvalidDelayTime();
            return;
        }
        AudioManager audioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
        audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, volume, 0);

        controller.setNavSpeed(navSpeed);
        SharedPreferences.Editor edit = SpManager.getInstance().edit();
        edit.putBoolean(Constant.VOICE_BROADCAST, voiceBroadcast == R.id.rb_open_voice_broadcast);
        edit.putBoolean(Constant.LOCK_SCREEN, lockScreen == R.id.rb_open_lock_screen);
        edit.putBoolean(Constant.OPEN_DETECTION,openDetection == R.id.rb_open_detection);
        edit.putString(Constant.NAV_SPEED, navSpeed);
        edit.putInt(Constant.LOW_POWER, lowPower);
        edit.putInt(Constant.SYS_VOLUME, volume);
        edit.putInt(Constant.DELAY_TIME, Integer.parseInt(delay));
        edit.apply();

        view.onSaveSuccess();
    }

    @Override
    public void onDismissOperation() {
        confirmCount = 0;
        firstEnteredPassword = "";
        view.onLockScreenDismiss();
    }

    @Override
    public void onLockScreenConfirmed(SetLockScreenPopupWindow window, EditText editText, String password, boolean isOpenLock) {
        if (password.length() < 4) {
            view.onLockScreenInvalid();
            return;
        }
        if (isOpenLock) {
            //打开锁屏
            confirmCount++;
            if (confirmCount == 1) {
                //第一次输入密码
                firstEnteredPassword = password;
                view.onLockScreenFirstPasswordConfirmed(editText);
            } else if (firstEnteredPassword.equals(password)) {
                //密码输入成功，打开锁屏
                SharedPreferences.Editor edit = SpManager.getInstance().edit();
                edit.putBoolean(Constant.LOCK_SCREEN, true);
                edit.putString(Constant.LOCK_SCREEN_PASSWORD, password);
                edit.apply();
                view.onOpenLockScreenSuccess(window);
            } else {
                view.onLockScreenPasswordNotSame(editText);
            }
        } else if (SpManager.getInstance().getString(Constant.LOCK_SCREEN_PASSWORD, "").equals(password)) {
            //关闭锁屏且密码正确
            SharedPreferences.Editor edit = SpManager.getInstance().edit();
            edit.remove(Constant.LOCK_SCREEN);
            edit.remove(Constant.LOCK_SCREEN_PASSWORD);
            edit.apply();
            view.onCloseLockScreenSuccess(window);
        } else {
            //关闭锁屏密码错误
            view.onCloseLockScreenPasswordError(editText);
        }
    }

    @Override
    public void onLogoutOrLogInClicked(Context context) {
        SharedPreferences instance = SpManager.getInstance();
        String username = instance.getString(Constant.USERNAME, "");
        if (!TextUtils.isEmpty(username)) {
            SharedPreferences.Editor edit = instance.edit();
            edit.remove(Constant.ACCESS_TOKEN);
            edit.remove(Constant.REFRESH_TOKEN);
            edit.remove(Constant.USERNAME);
            edit.remove(Constant.PASSWORD);
            edit.apply();
            view.onLogoutSuccess();
        } else {
            BaseActivity.start(context, LoginActivity.class, null);
        }
    }

    @Override
    public void onSaveDisinfectionPromptAudio(Context context, Editable text) {
        if (TextUtils.isEmpty(text)) {
            ToastUtils.showShortToast(context.getString(R.string.text_input_disinfection_prompt_first));
            return;
        }
        int locale = SpManager.getInstance().getInt(Constant.KEY_LANGUAGE_TYPE, Constant.DEFAULT_LANGUAGE_TYPE);
        String language = LocaleUtil.getLanguage(locale);
        File directory = context.getFilesDir();
        File file = new File(directory + "/disinfection_voice/voice_disinfection_prompt_temp_" + language + ".wav");
        if (!file.exists()) {
            ToastUtils.showShortToast(context.getString(R.string.text_not_found_audio_file));
            return;
        }
        boolean b = file.renameTo(new File(directory + "/disinfection_voice/voice_disinfection_prompt_" + language + ".wav"));
        if (b) {
            SpManager.getInstance().edit().putString(Constant.DISINFECTION_PROMPT, text.toString()).apply();
            view.onSaveAudioSuccess();
        }
    }

    @Override
    public void onTryListen(Context context, Editable text) {
        if (TextUtils.isEmpty(text)) {
            ToastUtils.showShortToast(context.getString(R.string.text_input_disinfection_prompt_first));
            return;
        }

        Observable
                .create(new ObservableOnSubscribe<Integer>() {
                    @Override
                    public void subscribe(@NonNull ObservableEmitter<Integer> emitter) throws Throwable {
                        emitter.onNext(1);
                        int localeType = SpManager.getInstance().getInt(Constant.KEY_LANGUAGE_TYPE, Constant.DEFAULT_LANGUAGE_TYPE);
                        String language = LocaleUtil.getLanguage(localeType);
                        String voice = LocaleUtil.getVoice(localeType);
                        SpeechConfig speechConfig = SpeechConfig.fromSubscription("", "eastasia");
                        speechConfig.setSpeechSynthesisLanguage(language);
                        speechConfig.setSpeechSynthesisVoiceName(voice);
                        speechConfig.setSpeechSynthesisOutputFormat(SpeechSynthesisOutputFormat.Riff24Khz16BitMonoPcm);
                        AudioConfig audioConfig = AudioConfig.fromDefaultSpeakerOutput();
                        SpeechSynthesizer synthesizer = new SpeechSynthesizer(speechConfig, audioConfig);
                        SpeechSynthesisResult result = synthesizer.SpeakText(text.toString());
                        ResultReason reason = result.getReason();
                        if (!reason.equals(ResultReason.SynthesizingAudioCompleted)) {
                            emitter.onError(new RuntimeException(reason.toString()));
                            return;
                        }
                        byte[] audioData = result.getAudioData();
                        try {
                            File root = new File(context.getFilesDir() + "/disinfection_voice");
                            if (!root.exists()) root.mkdir();
                            BufferedOutputStream bufferedOutputStream = new BufferedOutputStream(new FileOutputStream(context.getFilesDir() + "/disinfection_voice/voice_disinfection_prompt_temp_" + language + ".wav"));
                            bufferedOutputStream.write(audioData, 0, audioData.length);
                            bufferedOutputStream.flush();
                            bufferedOutputStream.close();
                            emitter.onNext(2);
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                })
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<Integer>() {
                    @Override
                    public void onSubscribe(@NonNull Disposable d) {

                    }

                    @Override
                    public void onNext(@NonNull Integer o) {
                        if (o == 1) {
                            view.onSynthesizeStart();
                        } else {
                            view.onSynthesizeEnd();
                        }
                    }

                    @Override
                    public void onError(@NonNull Throwable e) {
                        view.onSynthesizeError(e.getMessage());
                    }

                    @Override
                    public void onComplete() {

                    }
                });
    }


}
