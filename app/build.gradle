import java.text.SimpleDateFormat

plugins {
    id 'com.android.application'
}

android {
    compileSdkVersion 33
    namespace 'com.example.workomar'

    defaultConfig {
        applicationId "com.example.workomar"
        minSdkVersion 21
        targetSdkVersion 34
        versionCode 403
        versionName "v4.0.3"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        buildConfigField 'String', 'TAG', '"xuedong"'
        buildConfigField 'String', 'APP_LOG_DIR', '"disinfection"'
        buildConfigField 'String', 'BUGLY', '"fec460176e"'

        ndk {
            abiFilters 'armeabi-v7a', 'arm64-v8a', 'x86', 'x86_64'
        }
    }

    buildFeatures {
        buildConfig true
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
    java {
        toolchain {
            languageVersion = JavaLanguageVersion.of(17)
        }
    }
    sourceSets {
        main {
            jniLibs.srcDirs = ['jniLibs']
        }
    }

    applicationVariants.all { variant ->
        variant.outputs.all {
            outputFileName = "workomar_${defaultConfig.applicationId}_${defaultConfig.versionName}-${getCurrentTime()}.apk"
        }
    }
}

def static getCurrentTime() {
    SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmm")
    Date curDate = new Date(System.currentTimeMillis())
    return formatter.format(curDate)
}



dependencies {
    implementation fileTree(dir: 'libs', include: ['*.aar'])
    implementation 'androidx.appcompat:appcompat:1.2.0'
    implementation 'com.google.android.material:material:1.2.1'
    implementation 'androidx.constraintlayout:constraintlayout:2.0.1'
    // Gif player
    implementation 'pl.droidsonroids.gif:android-gif-drawable:1.2.22'
    // PickerView
    implementation 'io.github.skyofsky:Android-PickerView:4.1.11'
    // Bugly crash reporting
    implementation 'com.tencent.bugly:nativecrashreport:latest.release'
    implementation 'com.tencent.bugly:crashreport_upgrade:latest.release'
    // Retrofit & RxJava
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.squareup.retrofit2:adapter-rxjava3:2.9.0'
    implementation 'io.reactivex.rxjava3:rxjava:3.0.12'
    implementation 'io.reactivex.rxjava3:rxandroid:3.0.0'
    // Room database
    implementation "androidx.room:room-rxjava3:2.3.0"
    implementation "androidx.room:room-runtime:2.3.0"
    annotationProcessor "androidx.room:room-compiler:2.3.0"
    // Pull-to-refresh
    implementation "androidx.swiperefreshlayout:swiperefreshlayout:1.0.0"
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'com.kyleduo.switchbutton:library:2.1.0'
    implementation 'com.github.ybq:Android-SpinKit:1.4.0'
    // QR Code
    implementation 'com.google.zxing:core:3.3.0'
    // JWT
    implementation 'io.jsonwebtoken:jjwt-api:0.11.2'
    runtimeOnly 'io.jsonwebtoken:jjwt-impl:0.11.2'
    runtimeOnly('io.jsonwebtoken:jjwt-orgjson:0.11.2') {
        exclude group: 'org.json', module: 'json'
    }
    // Microsoft Cognitive Services
    implementation 'com.microsoft.cognitiveservices.speech:client-sdk:1.19.0'
    // ShadowDrawable
    implementation 'com.github.JuHonggang:ShadowDrawable:0.1'
    testImplementation 'junit:junit:4.+'
    androidTestImplementation 'androidx.test.ext:junit:1.1.2'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.3.0'
}