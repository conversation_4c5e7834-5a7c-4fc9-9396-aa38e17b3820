# Steribot Database Schema Documentation

## 📋 Database Overview

**Database Type:** SQLite with Room ORM  
**Database Name:** `db_task`  
**ORM Framework:** Android Room  
**Version:** 1  
**Package:** `com.reeman.robot.disinfection`

## 🗄️ Database Schema

### Table: `t_task`

**Description:** Core table storing all disinfection tasks (both manual and scheduled)

**Table Definition:**
```sql
CREATE TABLE t_task (
    tid INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
    t_cloud_id INTEGER DEFAULT -1,
    t_task_type INTEGER NOT NULL,
    t_task_name TEXT,
    t_task_mode INTEGER DEFAULT 0,
    t_switch_mode INTEGER DEFAULT 0,
    t_finish_action INTEGER DEFAULT 0,
    t_stay_time INTEGER DEFAULT 0,
    t_duration_time INTEGER DEFAULT 0,
    t_start_time TEXT,
    t_repeat_time INTEGER DEFAULT 0,
    t_enabled INTEGER DEFAULT 1,
    t_create_time TEXT,
    t_has_sync INTEGER DEFAULT 0,
    t_has_delete INTEGER DEFAULT 0
);
```

#### Field Definitions

| Field Name | Data Type | Constraints | Description |
|------------|-----------|-------------|-------------|
| `tid` | INTEGER | PRIMARY KEY, AUTOINCREMENT, NOT NULL | Local unique task identifier |
| `t_cloud_id` | INTEGER | DEFAULT -1 | Cloud synchronization ID (-1 = not synced) |
| `t_task_type` | INTEGER | NOT NULL | Task type: 0=Manual, 1=Scheduled |
| `t_task_name` | TEXT | NULLABLE | Human-readable task name |
| `t_task_mode` | INTEGER | DEFAULT 0 | Execution mode: 0=Single run, 1=Duration loop |
| `t_switch_mode` | INTEGER | DEFAULT 0 | UV switch mode: 0=Always on, 1=Target points only, 2=Always off |
| `t_finish_action` | INTEGER | DEFAULT 0 | Post-completion action: 0=Go to charge, 1=Return to start |
| `t_stay_time` | INTEGER | DEFAULT 0 | Stay duration at target points (seconds) |
| `t_duration_time` | INTEGER | DEFAULT 0 | Loop duration for duration mode (seconds) |
| `t_start_time` | TEXT | NULLABLE | Scheduled start time (ISO 8601 format) |
| `t_repeat_time` | INTEGER | DEFAULT 0 | Weekly repeat pattern (7-bit bitmask) |
| `t_enabled` | INTEGER | DEFAULT 1 | Task enabled status: 0=Disabled, 1=Enabled |
| `t_create_time` | TEXT | NULLABLE | Task creation timestamp (ISO 8601 format) |
| `t_has_sync` | INTEGER | DEFAULT 0 | Cloud sync status: 0=Not synced, 1=Synced |
| `t_has_delete` | INTEGER | DEFAULT 0 | Soft delete flag: 0=Active, 1=Deleted |

#### Indexes

```sql
-- Index for task type queries
CREATE INDEX idx_task_type ON t_task(t_task_type);

-- Index for sync status queries
CREATE INDEX idx_sync_status ON t_task(t_has_sync, t_has_delete);

-- Index for cloud synchronization
CREATE INDEX idx_cloud_sync ON t_task(t_cloud_id, t_has_sync);

-- Index for scheduled tasks
CREATE INDEX idx_scheduled_tasks ON t_task(t_task_type, t_enabled, t_has_delete);
```

#### Constraints and Business Rules

1. **Task Type Validation:**
   - `t_task_type` must be 0 (Manual) or 1 (Scheduled)
   - Manual tasks: Only one active manual task allowed
   - Scheduled tasks: Multiple allowed

2. **Cloud Synchronization Rules:**
   - `t_cloud_id = -1`: Task exists only locally
   - `t_cloud_id > 0`: Task synchronized with cloud
   - `t_has_sync = 0`: Requires synchronization
   - `t_has_sync = 1`: Synchronized with cloud

3. **Soft Delete Implementation:**
   - `t_has_delete = 1`: Task marked for deletion
   - Deleted tasks remain in database for sync purposes
   - Physical deletion occurs after cloud synchronization

4. **Repeat Pattern Encoding:**
   - 7-bit bitmask for weekly schedule
   - Bit 0: Monday, Bit 1: Tuesday, ..., Bit 6: Sunday
   - Example: 127 (binary: 1111111) = Every day

## 🔄 Data Access Patterns

### Room Entity Definition

```java
@Entity(tableName = "t_task")
public class Task {
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "tid")
    public long tid;

    @ColumnInfo(name = "t_cloud_id")
    public long cloudId = -1;

    @ColumnInfo(name = "t_task_type")
    public int taskType;

    @ColumnInfo(name = "t_task_name")
    public String taskName;

    @ColumnInfo(name = "t_task_mode")
    public int taskMode = 0;

    @ColumnInfo(name = "t_switch_mode")
    public int switchMode = 0;

    @ColumnInfo(name = "t_finish_action")
    public int finishAction = 0;

    @ColumnInfo(name = "t_stay_time")
    public long stayTime = 0;

    @ColumnInfo(name = "t_duration_time")
    public long durationTime = 0;

    @ColumnInfo(name = "t_start_time")
    public String startTime;

    @ColumnInfo(name = "t_repeat_time")
    public int repeatTime = 0;

    @ColumnInfo(name = "t_enabled")
    public boolean enabled = true;

    @ColumnInfo(name = "t_create_time")
    public String createTime;

    @ColumnInfo(name = "t_has_sync")
    public boolean hasSync = false;

    @ColumnInfo(name = "t_has_delete")
    public boolean hasDelete = false;
}
```

### Data Access Object (DAO)

```java
@Dao
public interface TaskDao {
    // Create operations
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    Single<Long> newTask(Task task);

    @Insert
    List<Long> insertAllScheduleTasks(List<Task> list);

    // Read operations
    @Query("SELECT * FROM t_task WHERE t_task_type = 0")
    Maybe<Task> getManualTask();

    @Query("SELECT * FROM t_task WHERE t_task_type = 1 AND t_has_delete = 0 ORDER BY t_start_time DESC")
    Single<List<Task>> getAllScheduledTask();

    @Query("SELECT * FROM t_task WHERE t_task_type = 1 AND t_has_delete = 0")
    List<Task> getAllScheduledTaskSync();

    // Update operations
    @Update(onConflict = OnConflictStrategy.REPLACE)
    Single<Integer> updateTask(Task task);

    @Query("UPDATE t_task SET t_enabled = :enable, t_has_sync = 0 WHERE tid = :tid")
    Single<Integer> updateTaskEnableState(long tid, boolean enable);

    // Delete operations (soft delete)
    @Query("UPDATE t_task SET t_has_delete = 1, t_has_sync = 0 WHERE tid = :tid")
    Single<Integer> deleteTask(long tid);

    @Query("DELETE FROM t_task WHERE tid = :tid")
    void deleteTaskDirectlySync(long tid);

    // Synchronization queries
    @Query("SELECT * FROM t_task WHERE t_task_type = 1 AND t_has_sync = 0")
    List<Task> getAllUnSyncTask();

    @Query("SELECT * FROM t_task WHERE t_task_type = 1 AND t_cloud_id = -1 AND t_has_sync = 0 AND t_has_delete = 0")
    List<Task> getAllUnCreatedTasks();

    @Query("SELECT * FROM t_task WHERE t_task_type = 1 AND t_cloud_id != -1 AND t_has_sync = 0 AND t_has_delete = 0")
    List<Task> getAllModifiedTask();

    @Query("SELECT * FROM t_task WHERE t_task_type = 1 AND t_has_delete = 1 AND t_has_sync = 0")
    List<Task> getAllDeleteAndUnSyncTasks();
}
```

## 📊 Sample Data

### Manual Task Example
```sql
INSERT INTO t_task (
    t_task_type, t_task_name, t_task_mode, t_switch_mode, 
    t_finish_action, t_stay_time, t_create_time
) VALUES (
    0, '手动任务', 0, 0, 0, 0, '2024-01-15T10:30:00Z'
);
```

### Scheduled Task Example
```sql
INSERT INTO t_task (
    t_task_type, t_task_name, t_task_mode, t_switch_mode,
    t_finish_action, t_stay_time, t_duration_time, t_start_time,
    t_repeat_time, t_enabled, t_create_time
) VALUES (
    1, 'Daily Disinfection', 1, 1, 0, 30, 3600, 
    '2024-01-15T09:00:00Z', 127, 1, '2024-01-15T08:00:00Z'
);
```

## 🔄 Data Synchronization Flow

### Local to Cloud Synchronization

1. **New Tasks (t_cloud_id = -1)**
   ```sql
   SELECT * FROM t_task 
   WHERE t_cloud_id = -1 AND t_has_sync = 0 AND t_has_delete = 0;
   ```

2. **Modified Tasks (t_cloud_id > 0, t_has_sync = 0)**
   ```sql
   SELECT * FROM t_task 
   WHERE t_cloud_id != -1 AND t_has_sync = 0 AND t_has_delete = 0;
   ```

3. **Deleted Tasks (t_has_delete = 1)**
   ```sql
   SELECT * FROM t_task 
   WHERE t_has_delete = 1 AND t_has_sync = 0;
   ```

### Cloud to Local Synchronization

1. **Download all cloud tasks**
2. **Merge with local tasks based on t_cloud_id**
3. **Update local tasks with cloud data**
4. **Mark as synchronized (t_has_sync = 1)**

## 🔍 Query Performance Optimization

### Common Query Patterns

1. **Get Active Scheduled Tasks**
   ```sql
   SELECT * FROM t_task 
   WHERE t_task_type = 1 AND t_enabled = 1 AND t_has_delete = 0
   ORDER BY t_start_time ASC;
   ```

2. **Get Unsynchronized Tasks**
   ```sql
   SELECT * FROM t_task 
   WHERE t_has_sync = 0
   ORDER BY t_create_time ASC;
   ```

3. **Get Today's Scheduled Tasks**
   ```sql
   SELECT * FROM t_task 
   WHERE t_task_type = 1 
   AND t_enabled = 1 
   AND t_has_delete = 0
   AND (t_repeat_time & :dayBitmask) > 0;
   ```

## 📈 Database Statistics

### Expected Data Volume
- **Manual Tasks:** 1 active task maximum
- **Scheduled Tasks:** 50-100 typical, 500 maximum
- **Total Records:** < 1000 tasks typical
- **Database Size:** < 1MB typical

### Performance Characteristics
- **Insert Performance:** < 10ms per task
- **Query Performance:** < 5ms for typical queries
- **Sync Performance:** < 100ms for full synchronization

## 🔧 Database Maintenance

### Cleanup Operations
```sql
-- Remove old deleted tasks (after cloud sync)
DELETE FROM t_task 
WHERE t_has_delete = 1 AND t_has_sync = 1 
AND t_create_time < date('now', '-30 days');

-- Vacuum database for space reclamation
VACUUM;
```

### Backup and Recovery
- **Automatic Backup:** Cloud synchronization serves as backup
- **Local Backup:** Export to JSON format
- **Recovery:** Re-download from cloud or restore from JSON

This database schema provides efficient storage and retrieval for the Steribot disinfection task management system with robust synchronization capabilities.
