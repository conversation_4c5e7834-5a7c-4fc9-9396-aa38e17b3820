@echo off
echo ========================================
echo Steribot App Installation and Test Script
echo ========================================
echo.

REM Check if ADB is available
where adb >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: ADB is not found in PATH
    echo Please install Android SDK Platform Tools and add to PATH
    echo Download from: https://developer.android.com/studio/releases/platform-tools
    pause
    exit /b 1
)

echo Checking for connected Android devices...
adb devices
echo.

REM Find the APK file
for %%f in (app\build\outputs\apk\debug\workomar_*.apk) do set APK_FILE=%%f

if not defined APK_FILE (
    echo ERROR: APK file not found in app\build\outputs\apk\debug\
    echo Please run: gradlew assembleDebug
    pause
    exit /b 1
)

echo Found APK: %APK_FILE%
echo.

echo Installing the app...
adb install -r "%APK_FILE%"

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ERROR: Failed to install APK
    echo Make sure:
    echo 1. Android device is connected and USB debugging is enabled
    echo 2. Device is authorized for debugging
    echo 3. Previous version is uninstalled or use -r flag for replacement
    pause
    exit /b 1
)

echo.
echo ========================================
echo Installation completed successfully!
echo ========================================
echo.

echo Starting the app...
adb shell am start -n com.example.workomar/.SplashActivity

echo.
echo Monitoring app logs (press Ctrl+C to stop)...
echo ========================================
adb logcat -s "workomar:*" "AndroidRuntime:E" "System.err:E"

pause
