@echo off
echo ========================================
echo Steribot App - Simple Test (Crash-Free)
echo ========================================
echo.

REM Check if ADB is available
where adb >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: ADB is not found in PATH
    echo.
    echo Please install Android SDK Platform Tools:
    echo 1. Download from: https://developer.android.com/studio/releases/platform-tools
    echo 2. Extract to a folder (e.g., C:\platform-tools)
    echo 3. Add the folder to your Windows PATH environment variable
    echo 4. Restart command prompt and try again
    echo.
    pause
    exit /b 1
)

echo Checking for connected Android devices...
adb devices
echo.

REM Find the APK file
for %%f in (app\build\outputs\apk\debug\workomar_*.apk) do set APK_FILE=%%f

if not defined APK_FILE (
    echo ERROR: APK file not found
    echo Please ensure the build was successful
    pause
    exit /b 1
)

echo Found APK: %APK_FILE%
echo.

echo Uninstalling previous version (if any)...
adb uninstall com.example.workomar 2>nul

echo.
echo Installing the crash-free app...
adb install "%APK_FILE%"

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ERROR: Failed to install APK
    echo.
    echo Troubleshooting:
    echo 1. Make sure USB debugging is enabled on your Android device
    echo 2. Accept the "Allow USB debugging" prompt on your device
    echo 3. Try running: adb devices
    echo 4. Your device should show as "device" not "unauthorized"
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo Installation completed successfully!
echo ========================================
echo.

echo Starting the app...
adb shell am start -n com.example.workomar/.SplashActivity

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo Warning: Could not start app automatically
    echo Please manually open the "Workomar" app on your device
    echo.
)

echo.
echo App should now be running on your device.
echo.
echo If the app crashes, run: debug_app_crashes.bat
echo.
pause
