@echo off
echo ========================================
echo Steribot App Crash Debug Script
echo ========================================
echo.

REM Check if ADB is available
where adb >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: ADB is not found in PATH
    echo Please install Android SDK Platform Tools and add to PATH
    pause
    exit /b 1
)

echo Clearing previous logs...
adb logcat -c

echo.
echo Starting crash monitoring...
echo Press Ctrl+C to stop monitoring
echo ========================================
echo.

REM Monitor for crashes and errors
adb logcat -v time | findstr /i "workomar\|crash\|exception\|error\|fatal\|androidruntime"
