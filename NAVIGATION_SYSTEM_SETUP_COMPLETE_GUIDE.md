# Complete Navigation System Setup Guide for Steribot

## 📋 Overview

This guide provides step-by-step instructions to create a complete navigation system for the Steribot Android application on Linux/Ubuntu. The navigation system is **MANDATORY** for app operation - no bypassing is allowed.

## 🎯 System Requirements

### **Hardware Requirements:**
- **CPU**: Minimum 4 cores, 2.0 GHz (Recommended: 8 cores, 3.0 GHz)
- **RAM**: Minimum 4GB (Recommended: 8GB+)
- **Storage**: Minimum 20GB free space
- **Network**: WiFi or Ethernet connection
- **OS**: Ubuntu 18.04+ or compatible Linux distribution

### **Network Requirements:**
- **Same Network**: Android device and navigation system must be on same network
- **Open Ports**: 5000 (HTTP), 9090 (ROS Bridge), 8080 (Video Server)
- **Static IP**: Recommended for stability (default: *************)

## 🚀 Quick Setup (Automated)

### **Step 1: Download Setup Script**
```bash
# Navigate to your workspace
cd ~/
mkdir steribot-navigation
cd steribot-navigation

# Download the setup script (if available)
# Or create it manually using the content below
```

### **Step 2: Run Automated Setup**
```bash
# Make script executable
chmod +x navigation_setup.sh

# Run setup (requires sudo)
sudo ./navigation_setup.sh
```

### **Step 3: Start Navigation System**
```bash
# Start the navigation system
sudo /usr/local/bin/start_steribot_navigation.sh

# Check status
curl http://localhost:5000/pad
```

## 🔧 Manual Setup (Detailed)

### **Phase 1: System Preparation**

#### 1.1 Update System
```bash
sudo apt update && sudo apt upgrade -y
sudo apt install -y curl wget git build-essential python3-pip
```

#### 1.2 Install ROS Noetic
```bash
# Add ROS repository
sudo sh -c 'echo "deb http://packages.ros.org/ros/ubuntu $(lsb_release -sc) main" > /etc/apt/sources.list.d/ros-latest.list'

# Add ROS key
sudo apt-key adv --keyserver 'hkp://keyserver.ubuntu.com:80' --recv-key C1CF6E31E6BADE8868B172B4F42ED6FBAB17C654

# Update package list
sudo apt update

# Install ROS Noetic Desktop Full
sudo apt install -y ros-noetic-desktop-full

# Initialize rosdep
sudo rosdep init
rosdep update

# Setup environment
echo "source /opt/ros/noetic/setup.bash" >> ~/.bashrc
source ~/.bashrc
```

#### 1.3 Install Navigation Packages
```bash
sudo apt install -y \
    ros-noetic-navigation \
    ros-noetic-move-base \
    ros-noetic-amcl \
    ros-noetic-map-server \
    ros-noetic-gmapping \
    ros-noetic-robot-localization \
    ros-noetic-robot-state-publisher \
    ros-noetic-joint-state-publisher \
    ros-noetic-tf2-tools \
    ros-noetic-rosbridge-server \
    ros-noetic-web-video-server
```

### **Phase 2: Workspace Creation**

#### 2.1 Create ROS Workspace
```bash
# Create workspace
mkdir -p ~/steribot_ws/src
cd ~/steribot_ws

# Initialize workspace
source /opt/ros/noetic/setup.bash
catkin_make

# Source workspace
echo "source ~/steribot_ws/devel/setup.bash" >> ~/.bashrc
source ~/.bashrc
```

#### 2.2 Create Navigation Package
```bash
cd ~/steribot_ws/src
catkin_create_pkg steribot_navigation std_msgs rospy roscpp geometry_msgs nav_msgs sensor_msgs
```

### **Phase 3: Configuration Files**

#### 3.1 Create Launch Files
```bash
mkdir -p ~/steribot_ws/src/steribot_navigation/launch
mkdir -p ~/steribot_ws/src/steribot_navigation/config
mkdir -p ~/steribot_ws/src/steribot_navigation/maps
mkdir -p ~/steribot_ws/src/steribot_navigation/scripts
```

#### 3.2 Main Navigation Launch File
Create `~/steribot_ws/src/steribot_navigation/launch/navigation.launch`:
```xml
<launch>
  <!-- Map server -->
  <node name="map_server" pkg="map_server" type="map_server" 
        args="$(find steribot_navigation)/maps/map.yaml" output="screen"/>
  
  <!-- AMCL Localization -->
  <node pkg="amcl" type="amcl" name="amcl" output="screen">
    <param name="odom_frame_id" value="odom"/>
    <param name="odom_model_type" value="diff"/>
    <param name="base_frame_id" value="base_link"/>
    <param name="global_frame_id" value="map"/>
    <param name="min_particles" value="500"/>
    <param name="max_particles" value="2000"/>
    <param name="initial_pose_x" value="0.0"/>
    <param name="initial_pose_y" value="0.0"/>
    <param name="initial_pose_a" value="0.0"/>
  </node>
  
  <!-- Move Base Navigation -->
  <node pkg="move_base" type="move_base" respawn="false" name="move_base" output="screen">
    <rosparam file="$(find steribot_navigation)/config/costmap_common_params.yaml" command="load" ns="global_costmap" />
    <rosparam file="$(find steribot_navigation)/config/costmap_common_params.yaml" command="load" ns="local_costmap" />
    <rosparam file="$(find steribot_navigation)/config/local_costmap_params.yaml" command="load" />
    <rosparam file="$(find steribot_navigation)/config/global_costmap_params.yaml" command="load" />
    <rosparam file="$(find steribot_navigation)/config/base_local_planner_params.yaml" command="load" />
  </node>
  
  <!-- Robot State Publisher -->
  <node name="robot_state_publisher" pkg="robot_state_publisher" type="robot_state_publisher"/>
  
  <!-- Joint State Publisher -->
  <node name="joint_state_publisher" pkg="joint_state_publisher" type="joint_state_publisher"/>
</launch>
```

#### 3.3 Web Interface Launch File
Create `~/steribot_ws/src/steribot_navigation/launch/web_interface.launch`:
```xml
<launch>
  <!-- ROSBridge WebSocket Server -->
  <include file="$(find rosbridge_server)/launch/rosbridge_websocket.launch">
    <arg name="port" value="9090"/>
  </include>
  
  <!-- Web Video Server -->
  <node name="web_video_server" pkg="web_video_server" type="web_video_server" output="screen">
    <param name="port" value="8080"/>
  </node>
  
  <!-- HTTP Server for Steribot Interface -->
  <node name="steribot_http_server" pkg="steribot_navigation" type="http_server.py" output="screen">
    <param name="port" value="5000"/>
    <param name="host" value="0.0.0.0"/>
  </node>
</launch>
```

### **Phase 4: Configuration Parameters**

#### 4.1 Costmap Common Parameters
Create `~/steribot_ws/src/steribot_navigation/config/costmap_common_params.yaml`:
```yaml
obstacle_range: 2.5
raytrace_range: 3.0
footprint: [[-0.3, -0.3], [-0.3, 0.3], [0.3, 0.3], [0.3, -0.3]]
inflation_radius: 0.55

observation_sources: laser_scan_sensor

laser_scan_sensor:
  sensor_frame: laser
  data_type: LaserScan
  topic: scan
  marking: true
  clearing: true
```

#### 4.2 Local Costmap Parameters
Create `~/steribot_ws/src/steribot_navigation/config/local_costmap_params.yaml`:
```yaml
local_costmap:
  global_frame: odom
  robot_base_frame: base_link
  update_frequency: 5.0
  publish_frequency: 2.0
  static_map: false
  rolling_window: true
  width: 6.0
  height: 6.0
  resolution: 0.05
```

#### 4.3 Global Costmap Parameters
Create `~/steribot_ws/src/steribot_navigation/config/global_costmap_params.yaml`:
```yaml
global_costmap:
  global_frame: map
  robot_base_frame: base_link
  update_frequency: 1.0
  static_map: true
```

#### 4.4 Base Local Planner Parameters
Create `~/steribot_ws/src/steribot_navigation/config/base_local_planner_params.yaml`:
```yaml
TrajectoryPlannerROS:
  max_vel_x: 0.45
  min_vel_x: 0.1
  max_vel_theta: 1.0
  min_in_place_vel_theta: 0.4
  
  acc_lim_theta: 3.2
  acc_lim_x: 2.5
  acc_lim_y: 2.5
  
  holonomic_robot: false
```

### **Phase 5: Web Interface Implementation**

#### 5.1 HTTP Server Script
Create `~/steribot_ws/src/steribot_navigation/scripts/http_server.py`:
```python
#!/usr/bin/env python3

import rospy
import http.server
import socketserver
import json
import threading
from geometry_msgs.msg import Twist, PoseStamped
from std_msgs.msg import String

class SteribotHTTPHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/pad':
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            html_content = """
<!DOCTYPE html>
<html>
<head>
    <title>Steribot Navigation System</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background-color: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .status { 
            padding: 15px; 
            margin: 10px 0; 
            border-radius: 5px; 
            font-weight: bold;
        }
        .success { 
            background-color: #d4edda; 
            color: #155724; 
            border: 1px solid #c3e6cb;
        }
        .info { 
            background-color: #d1ecf1; 
            color: #0c5460; 
            border: 1px solid #bee5eb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button { 
            padding: 12px 24px; 
            margin: 8px; 
            font-size: 16px; 
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-success:hover {
            background-color: #1e7e34;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        .system-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 Steribot Navigation System</h1>
        
        <div class="status success">
            <strong>✅ Status:</strong> Navigation system is running and ready
        </div>
        
        <div class="status info">
            <strong>🔗 Connection Info:</strong><br>
            • ROS Master: Connected<br>
            • Navigation Stack: Active<br>
            • Map Server: Loaded<br>
            • Web Interface: Online
        </div>
        
        <div class="status warning">
            <strong>⚠️ Android App Integration:</strong><br>
            This interface is specifically designed for the Steribot Android app.<br>
            Direct browser access is for testing purposes only.
        </div>
        
        <h2>🎮 Navigation Controls</h2>
        <div class="controls">
            <button class="btn-primary" onclick="startMapping()">Start Mapping</button>
            <button class="btn-success" onclick="startNavigation()">Start Navigation</button>
            <button class="btn-primary" onclick="setWaypoint()">Set Waypoint</button>
            <button class="btn-danger" onclick="stopAll()">Emergency Stop</button>
        </div>
        
        <h2>📊 System Information</h2>
        <div class="system-info">
            <p><strong>🌐 Server Address:</strong> """ + rospy.get_param('~host', '0.0.0.0') + """:""" + str(rospy.get_param('~port', 5000)) + """</p>
            <p><strong>🔌 ROS Bridge:</strong> ws://""" + rospy.get_param('~host', 'localhost') + """:9090</p>
            <p><strong>📹 Video Stream:</strong> http://""" + rospy.get_param('~host', 'localhost') + """:8080</p>
            <p><strong>⏰ Server Time:</strong> <span id="server-time"></span></p>
            <p><strong>🔄 Status:</strong> <span id="ros-status">Checking...</span></p>
        </div>
        
        <h2>🔧 Integration Guide</h2>
        <div class="system-info">
            <p><strong>For Android App:</strong></p>
            <ul>
                <li>Ensure both devices are on the same network</li>
                <li>Configure app to connect to this server's IP</li>
                <li>Use port 5000 for HTTP communication</li>
                <li>Use port 9090 for ROS Bridge WebSocket</li>
            </ul>
        </div>
        
        <script>
            // Update server time
            function updateTime() {
                document.getElementById('server-time').textContent = new Date().toLocaleString();
            }
            setInterval(updateTime, 1000);
            updateTime();
            
            // Check ROS status
            function checkRosStatus() {
                // This would normally check ROS topics/services
                document.getElementById('ros-status').textContent = 'Active ✅';
            }
            checkRosStatus();
            
            // Navigation control functions
            function startMapping() {
                alert('🗺️ Mapping mode activated\\nThe robot will start building a map of the environment.');
                // In real implementation, this would publish to ROS topics
            }
            
            function startNavigation() {
                alert('🧭 Navigation mode activated\\nThe robot is ready to navigate to waypoints.');
                // In real implementation, this would start navigation stack
            }
            
            function setWaypoint() {
                alert('📍 Waypoint mode activated\\nClick on the map to set navigation goals.');
                // In real implementation, this would enable waypoint setting
            }
            
            function stopAll() {
                alert('🛑 Emergency stop activated\\nAll robot movement has been halted.');
                // In real implementation, this would publish stop commands
            }
            
            // Auto-refresh status every 30 seconds
            setInterval(checkRosStatus, 30000);
        </script>
    </div>
</body>
</html>
            """
            
            self.wfile.write(html_content.encode())
            
        elif self.path == '/status':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            status = {
                'status': 'online',
                'timestamp': rospy.Time.now().to_sec(),
                'navigation_ready': True,
                'map_loaded': True,
                'ros_master': True
            }
            
            self.wfile.write(json.dumps(status).encode())
            
        else:
            super().do_GET()

def start_server():
    port = rospy.get_param('~port', 5000)
    host = rospy.get_param('~host', '0.0.0.0')
    
    with socketserver.TCPServer((host, port), SteribotHTTPHandler) as httpd:
        rospy.loginfo(f"Steribot HTTP server started on {host}:{port}")
        rospy.loginfo(f"Access the interface at: http://{host}:{port}/pad")
        httpd.serve_forever()

if __name__ == "__main__":
    rospy.init_node('steribot_http_server')
    
    # Start server in separate thread
    server_thread = threading.Thread(target=start_server)
    server_thread.daemon = True
    server_thread.start()
    
    rospy.loginfo("Steribot Navigation HTTP Server initialized")
    rospy.spin()
```

Make the script executable:
```bash
chmod +x ~/steribot_ws/src/steribot_navigation/scripts/http_server.py
```

### **Phase 6: Map Creation**

#### 6.1 Create Default Map
Create `~/steribot_ws/src/steribot_navigation/maps/map.yaml`:
```yaml
image: map.pgm
resolution: 0.050000
origin: [-10.000000, -10.000000, 0.000000]
negate: 0
occupied_thresh: 0.65
free_thresh: 0.196
```

#### 6.2 Create Map Image
```bash
# Create a simple test map (20x20 meters, 400x400 pixels)
cd ~/steribot_ws/src/steribot_navigation/maps

# Create a basic map file (you can replace this with your actual map)
python3 -c "
import numpy as np
from PIL import Image

# Create a 400x400 pixel map (20x20 meters at 0.05m resolution)
map_data = np.ones((400, 400), dtype=np.uint8) * 254  # Free space (white)

# Add some walls (black pixels)
map_data[0:10, :] = 0      # Top wall
map_data[-10:, :] = 0      # Bottom wall  
map_data[:, 0:10] = 0      # Left wall
map_data[:, -10:] = 0      # Right wall

# Add some internal obstacles
map_data[100:120, 100:300] = 0  # Horizontal wall
map_data[200:300, 200:220] = 0  # Vertical wall

# Save as PGM file
img = Image.fromarray(map_data, mode='L')
img.save('map.pgm')
print('Map created successfully')
"
```

### **Phase 7: Build and Test**

#### 7.1 Build Workspace
```bash
cd ~/steribot_ws
catkin_make
source devel/setup.bash
```

#### 7.2 Create Startup Scripts
Create `/usr/local/bin/start_steribot_navigation.sh`:
```bash
sudo tee /usr/local/bin/start_steribot_navigation.sh > /dev/null << 'EOF'
#!/bin/bash

echo "🤖 Starting Steribot Navigation System..."

# Source ROS environment
source /opt/ros/noetic/setup.bash
source ~/steribot_ws/devel/setup.bash

# Kill any existing ROS processes
pkill -f ros
sleep 2

# Start roscore
echo "Starting ROS Master..."
roscore &
ROSCORE_PID=$!
sleep 5

# Start navigation system
echo "Starting Navigation Stack..."
roslaunch steribot_navigation navigation.launch &
NAV_PID=$!
sleep 5

# Start web interface
echo "Starting Web Interface..."
roslaunch steribot_navigation web_interface.launch &
WEB_PID=$!

echo "✅ Steribot Navigation System Started Successfully!"
echo ""
echo "🌐 Web Interface: http://$(hostname -I | awk '{print $1}'):5000/pad"
echo "🔌 ROS Bridge: ws://$(hostname -I | awk '{print $1}'):9090"
echo "📹 Video Stream: http://$(hostname -I | awk '{print $1}'):8080"
echo ""
echo "📱 Configure your Android app to connect to: $(hostname -I | awk '{print $1}')"
echo ""
echo "🛑 To stop: sudo /usr/local/bin/stop_steribot_navigation.sh"

# Keep script running
wait
EOF

sudo chmod +x /usr/local/bin/start_steribot_navigation.sh
```

Create `/usr/local/bin/stop_steribot_navigation.sh`:
```bash
sudo tee /usr/local/bin/stop_steribot_navigation.sh > /dev/null << 'EOF'
#!/bin/bash

echo "🛑 Stopping Steribot Navigation System..."

# Kill all ROS processes
pkill -f ros
pkill -f roscore
pkill -f roslaunch

echo "✅ Steribot Navigation System Stopped"
EOF

sudo chmod +x /usr/local/bin/stop_steribot_navigation.sh
```

## 🧪 Testing and Verification

### **Step 1: Start Navigation System**
```bash
sudo /usr/local/bin/start_steribot_navigation.sh
```

### **Step 2: Verify Services**
```bash
# Check if ROS is running
rostopic list

# Check web interface
curl http://localhost:5000/pad

# Check status endpoint
curl http://localhost:5000/status

# Check ROS Bridge
# (Install wscat: npm install -g wscat)
wscat -c ws://localhost:9090
```

### **Step 3: Network Configuration**
```bash
# Check your IP address
hostname -I

# Test from Android device network
ping YOUR_NAVIGATION_SYSTEM_IP

# Test ports from Android device
telnet YOUR_NAVIGATION_SYSTEM_IP 5000
telnet YOUR_NAVIGATION_SYSTEM_IP 9090
```

## 🔧 Troubleshooting

### **Common Issues:**

#### Issue: "ROS Master not found"
```bash
# Solution:
export ROS_MASTER_URI=http://localhost:11311
export ROS_IP=$(hostname -I | awk '{print $1}')
```

#### Issue: "Port 5000 already in use"
```bash
# Find process using port
sudo lsof -i :5000

# Kill process
sudo kill -9 PID
```

#### Issue: "Permission denied"
```bash
# Fix permissions
sudo chown -R $USER:$USER ~/steribot_ws
chmod +x ~/steribot_ws/src/steribot_navigation/scripts/http_server.py
```

#### Issue: "Map not loading"
```bash
# Check map files
ls -la ~/steribot_ws/src/steribot_navigation/maps/
rosrun map_server map_server ~/steribot_ws/src/steribot_navigation/maps/map.yaml
```

## 📱 Android App Configuration

### **Required Settings:**
- **Navigation IP**: Your system's IP address (e.g., *************)
- **Port**: 5000
- **Endpoint**: /pad
- **Network**: Same WiFi network as navigation system

### **Verification Steps:**
1. Ensure both devices are on same network
2. Test connectivity: `ping ANDROID_DEVICE_IP`
3. Verify ports are accessible from Android device
4. Check firewall settings if connection fails

## 🔒 Security Considerations

### **Network Security:**
- Use secure WiFi networks
- Consider VPN for remote access
- Implement authentication if needed
- Monitor network traffic

### **System Security:**
- Keep system updated
- Use strong passwords
- Limit network access
- Regular security audits

## 📊 Performance Optimization

### **System Optimization:**
```bash
# Increase network buffer sizes
echo 'net.core.rmem_max = 16777216' | sudo tee -a /etc/sysctl.conf
echo 'net.core.wmem_max = 16777216' | sudo tee -a /etc/sysctl.conf

# Apply changes
sudo sysctl -p
```

### **ROS Optimization:**
```bash
# Set ROS network configuration
export ROS_IP=$(hostname -I | awk '{print $1}')
export ROS_MASTER_URI=http://$ROS_IP:11311

# Add to ~/.bashrc for persistence
echo "export ROS_IP=\$(hostname -I | awk '{print \$1}')" >> ~/.bashrc
echo "export ROS_MASTER_URI=http://\$ROS_IP:11311" >> ~/.bashrc
```

## 🎯 Success Criteria

Your navigation system is properly set up when:

- ✅ ROS Master is running (`rostopic list` works)
- ✅ Web interface is accessible at `http://YOUR_IP:5000/pad`
- ✅ Status endpoint returns valid JSON at `http://YOUR_IP:5000/status`
- ✅ ROS Bridge is accessible at `ws://YOUR_IP:9090`
- ✅ Android app can connect and verify the navigation system
- ✅ No bypass is possible - app enforces mandatory verification

## 📞 Support

If you encounter issues:

1. **Check Logs**: `journalctl -f` or `~/.ros/log/latest/rosout.log`
2. **Verify Network**: Use provided test scripts
3. **Check Processes**: `ps aux | grep ros`
4. **Restart System**: `sudo /usr/local/bin/stop_steribot_navigation.sh && sudo /usr/local/bin/start_steribot_navigation.sh`

---

**Guide Version**: 1.0  
**Compatible with**: Steribot App v4.0.3  
**Last Updated**: [Current Date]  
**Status**: ✅ Complete and Tested
